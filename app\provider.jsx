"use client";

import { toast } from "sonner";
import { useMutation } from "convex/react";
import { useUser } from "@stackframe/stack";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

import { api } from "@/convex/_generated/api";
import { UserDetailContext } from "@/context/UserDetailContext";

function Provider({ children }) {
  const user = useUser();
  const pathname = usePathname();
  const [retryCount, setRetryCount] = useState(0);
  const [userDetail, setUserDetail] = useState(null);
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const createNewUserMutation = useMutation(api.users.CreateNewUser);

  // Daftar route yang memerlukan autentifikasi
  const protectedRoutes = ["/workspace", "/design"];
  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  );

  useEffect(() => {
    if (
      user &&
      (!userDetail || (userDetail.error && !userDetail.permanent)) &&
      !isCreatingUser
    ) {
      CreateUser();
    }
  }, [user, userDetail, isCreatingUser]);

  // Timeout untuk mencegah loading terlalu lama
  useEffect(() => {
    if (isCreatingUser) {
      const timeout = setTimeout(() => {
        console.warn("User creation timeout");
        setIsCreatingUser(false);
        setUserDetail({ error: true });
        toast.error("Timeout saat membuat user, mencoba ulang...");
      }, 15000); // 15 detik timeout

      return () => clearTimeout(timeout);
    }
  }, [isCreatingUser]);

  const CreateUser = async () => {
    if (isCreatingUser) return; // Prevent multiple calls

    setIsCreatingUser(true);
    console.log("Creating user attempt:", retryCount + 1);

    try {
      const data = {
        name: user?.displayName,
        email: user?.primaryEmail,
        picture: user?.profileImageUrl,
      };

      console.log("User data:", data);

      const result = await createNewUserMutation({
        ...data,
      });

      console.log("User created successfully:", result);
      setUserDetail(result);
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error("Error creating user:", error);

      if (retryCount < 3) {
        toast.error(
          `Gagal membuat user, mencoba ulang... (${retryCount + 1}/3)`
        );
        setRetryCount((prev) => prev + 1);

        // Set error state untuk trigger retry
        setUserDetail({ error: true });

        // Auto retry dengan exponential backoff
        const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
        setTimeout(() => {
          setUserDetail(null);
        }, delay);
      } else {
        toast.error(
          "Gagal membuat user setelah beberapa percobaan. Silakan refresh halaman."
        );
        setUserDetail({ error: true, permanent: true });
      }
    } finally {
      setIsCreatingUser(false);
    }
  };

  // Jika user belum login dan mencoba akses protected route, redirect ke login
  if (!user && isProtectedRoute) {
    if (typeof window !== "undefined") {
      window.location.href = `/handler/sign-in?after_auth_return_to=${encodeURIComponent(pathname)}`;
    }
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white shadow-lg border p-6 rounded-lg flex items-center gap-3">
          <div className="animate-spin w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full"></div>
          <span className="text-gray-700 font-medium">
            Mengarahkan ke login...
          </span>
        </div>
      </div>
    );
  }

  // Show loading untuk protected routes saat user sudah login tapi userDetail belum ready
  // Perbaikan: Tampilkan loading jika user ada tapi userDetail belum siap (termasuk saat error)
  if (
    user &&
    isProtectedRoute &&
    (!userDetail || (userDetail.error && !userDetail.permanent))
  ) {
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white shadow-lg border p-6 rounded-lg flex items-center gap-3">
          <div className="animate-spin w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full"></div>
          <span className="text-gray-700 font-medium">
            {userDetail?.error
              ? `Mencoba ulang... (${retryCount}/3)`
              : "Menyiapkan workspace Anda..."}
          </span>
        </div>
      </div>
    );
  }

  // Show error state untuk permanent error
  if (user && isProtectedRoute && userDetail?.error && userDetail?.permanent) {
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white shadow-lg border p-6 rounded-lg flex flex-col items-center gap-4 max-w-md">
          <div className="text-red-500 text-4xl">⚠️</div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Gagal Memuat Workspace
            </h3>
            <p className="text-gray-600 mb-4">
              Terjadi masalah saat menyiapkan workspace Anda. Silakan refresh
              halaman atau coba lagi nanti.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium"
            >
              Refresh Halaman
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <UserDetailContext.Provider
        value={{ userDetail, setUserDetail, isCreatingUser }}
      >
        {children}
      </UserDetailContext.Provider>
    </div>
  );
}

export default Provider;

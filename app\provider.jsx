"use client";

import { toast } from "sonner";
import { useMutation } from "convex/react";
import { useUser } from "@stackframe/stack";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

import { api } from "@/convex/_generated/api";
import { UserDetailContext } from "@/context/UserDetailContext";

function Provider({ children }) {
  const user = useUser();
  const pathname = usePathname();
  const [userDetail, setUserDetail] = useState(null);
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const createNewUserMutation = useMutation(api.users.CreateNewUser);

  // Daftar route yang memerlukan autentifikasi
  const protectedRoutes = ["/workspace", "/design"];
  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  );

  useEffect(() => {
    if (user && !userDetail && !isCreatingUser) {
      CreateUser();
    }
  }, [user, userDetail, isCreatingUser]);

  const CreateUser = async () => {
    if (isCreatingUser) return; // Prevent multiple calls

    setIsCreatingUser(true);
    try {
      const data = {
        name: user?.displayName,
        email: user?.primaryEmail,
        picture: user?.profileImageUrl,
      };

      const result = await createNewUserMutation({
        ...data,
      });

      setUserDetail(result);
    } catch (error) {
      toast.error(error.message || "Gagal membuat user");
      // Set userDetail to empty object to prevent infinite loading
      setUserDetail({ error: true });
    } finally {
      setIsCreatingUser(false);
    }
  };

  // Jika user belum login dan mencoba akses protected route, redirect ke login
  if (!user && isProtectedRoute) {
    if (typeof window !== "undefined") {
      window.location.href = `/handler/sign-in?after_auth_return_to=${encodeURIComponent(pathname)}`;
    }
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white shadow-lg border p-6 rounded-lg flex items-center gap-3">
          <div className="animate-spin w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full"></div>
          <span className="text-gray-700 font-medium">
            Mengarahkan ke login...
          </span>
        </div>
      </div>
    );
  }

  // Show loading untuk protected routes saat user sudah login tapi userDetail belum ready
  if (user && isProtectedRoute && !userDetail && isCreatingUser) {
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white shadow-lg border p-6 rounded-lg flex items-center gap-3">
          <div className="animate-spin w-6 h-6 border-2 border-purple-600 border-t-transparent rounded-full"></div>
          <span className="text-gray-700 font-medium">
            Menyiapkan workspace Anda...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <UserDetailContext.Provider
        value={{ userDetail, setUserDetail, isCreatingUser }}
      >
        {children}
      </UserDetailContext.Provider>
    </div>
  );
}

export default Provider;

import Link from "next/link";
import Image from "next/image";
import { toast } from "sonner";
import ImageKit from "imagekit";
import { useMutation } from "convex/react";
import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Download, Save } from "lucide-react";
import { UserButton } from "@stackframe/stack";

import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { useCanvasHook } from "@/hooks/useCanvas";

import ExportOptionsDialog from "./ExportOptionsDialog";

function DesignHeader({ DesignInfo }) {
  const { designId } = useParams();
  const { canvasEditor } = useCanvasHook();
  const SaveDesign = useMutation(api.designs.SaveDesign);
  const RenameDesign = useMutation(api.designs.RenameDesign);

  const [designName, setDesignName] = useState("");
  const [isRenaming, setIsRenaming] = useState(false);

  // Update local state when DesignInfo changes
  useEffect(() => {
    if (DesignInfo?.name) {
      setDesignName(DesignInfo.name);
    }
  }, [DesignInfo?.name]);

  var imagekit = new ImageKit({
    publicKey: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY,
    privateKey: process.env.NEXT_PUBLIC_IMAGEKIT_PRIVATE_KEY,
    urlEndpoint: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT,
  });

  /**
   * Gunakan untuk menyimpan desain ke database
   */
  const onSave = async () => {
    try {
      toast("Menyimpan...!");
      if (canvasEditor) {
        const base64Image = canvasEditor?.toDataURL({
          format: "png",
          quality: 0.8, // Increase quality for better preview
        });

        //Get List of Files
        const existingFiles = await imagekit.listFiles({
          searchQuery: `name="${designId}.png"`,
        });

        //Delete Old File of Exist
        if (existingFiles[0]?.fileId) {
          console.log("Deleting old file:", existingFiles[0]?.fileId);
          await imagekit.deleteFile(existingFiles[0]?.fileId);
        }

        // Upload with unique filename to force new URL
        const uniqueFileName = `${designId}_${Date.now()}.png`;
        console.log("Uploading new file:", uniqueFileName);

        const imageRef = await imagekit.upload({
          file: base64Image,
          fileName: uniqueFileName,
          isPublished: true,
          useUniqueFileName: false,
        });

        const JsonDesign = canvasEditor?.toJSON();

        // Use the new URL directly without additional timestamp
        const result = await SaveDesign({
          id: designId,
          jsonDesign: JsonDesign,
          imagePreview: imageRef?.url,
        });

        console.log("Design saved successfully:", result);
        console.log("New image URL:", imageRef?.url);

        toast("Disimpan!");
      }
    } catch (error) {
      console.error("Error saving design:", error);
      toast("Gagal menyimpan desain!");
    }
  };

  /**
   * Handle rename design
   */
  const handleRename = async () => {
    if (!designName.trim() || designName === DesignInfo?.name) {
      setDesignName(DesignInfo?.name || "");
      setIsRenaming(false);
      return;
    }

    try {
      setIsRenaming(true);
      await RenameDesign({
        id: designId,
        name: designName.trim(),
      });
      toast.success("Nama desain berhasil diubah!");
      setIsRenaming(false);
    } catch (error) {
      console.error("Error renaming design:", error);
      toast.error("Gagal mengubah nama desain!");
      setDesignName(DesignInfo?.name || "");
      setIsRenaming(false);
    }
  };

  /**
   * Handle input key down
   */
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.target.blur();
    }
  };

  return (
    <div className="p-2 flex justify-between items-center bg-gradient-to-r from-purple-700 via-blue-400 to-sky-600">
      <Link href={"/workspace"}>
        <Image src={"/logo.png"} alt="logo" width={100} height={60} />
      </Link>
      <input
        placeholder="Nama Desain"
        className="text-white border-none outline-none bg-transparent px-2 py-1 rounded"
        value={designName}
        onChange={(e) => setDesignName(e.target.value)}
        onBlur={handleRename}
        onKeyDown={handleKeyDown}
        disabled={isRenaming}
      />
      <div className="flex gap-5">
        <Button onClick={onSave}>
          <Save /> Simpan
        </Button>
        <ExportOptionsDialog designName={DesignInfo?.name || "CanvaInDesign"}>
          <Button>
            <Download /> Export
          </Button>
        </ExportOptionsDialog>
        <UserButton />
      </div>
    </div>
  );
}

export default DesignHeader;

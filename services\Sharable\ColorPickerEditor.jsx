import { ChromePicker, CirclePicker } from "react-color";

function ColorPickerEditor({ value, onColorChange, enableAlpha = true }) {
  const handleColorChange = (color) => {
    if (enableAlpha && color.rgb) {
      // Jika alpha enabled, gunakan rgba format
      const { r, g, b, a } = color.rgb;
      const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a})`;
      onColorChange(rgbaColor);
    } else {
      // Fallback ke hex untuk backward compatibility
      onColorChange(color.hex);
    }
  };

  return (
    <div className="space-y-3">
      <ChromePicker
        color={value}
        onChange={handleColorChange}
        disableAlpha={!enableAlpha}
        className="border-r rounded-2xl mb-3"
      />

      <CirclePicker color={value} onChange={(e) => onColorChange(e.hex)} />
    </div>
  );
}

export default ColorPickerEditor;

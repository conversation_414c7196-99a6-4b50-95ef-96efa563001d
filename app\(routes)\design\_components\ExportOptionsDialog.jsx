import Link from "next/link";
import { useQuery } from "convex/react";
import { useState, useContext } from "react";
import { Download, Loader2Icon, Crown, Lock } from "lucide-react";

import { api } from "@/convex/_generated/api";
import { useExportHook } from "@/hooks/useExport";
import { useCanvasHook } from "@/hooks/useCanvas";
import { UserDetailContext } from "@/context/UserDetailContext";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import {
  ExportFormats,
  getDefaultOptions,
  getAvailableFormats,
} from "@/services/Options/ExportOptions";

function ExportOptionsDialog({ children, designName }) {
  const { canvasEditor } = useCanvasHook();
  const { userDetail } = useContext(UserDetailContext);
  const { isExporting, exportWithFormat } = useExportHook(
    canvasEditor,
    designName
  );
  const [selectedFormat, setSelectedFormat] = useState("png");
  const [exportOptions, setExportOptions] = useState(getDefaultOptions("png"));
  const [isOpen, setIsOpen] = useState(false);

  // Get user subscription info
  const subscriptionInfo = useQuery(
    api.users.GetUserSubscriptionInfo,
    userDetail?._id ? { userId: userDetail._id } : "skip"
  );

  const isUserPremium = subscriptionInfo?.isPremium || false;
  const availableFormats = getAvailableFormats(isUserPremium);

  const handleFormatChange = (formatId) => {
    setSelectedFormat(formatId);
    setExportOptions(getDefaultOptions(formatId));
  };

  const handleOptionChange = (optionKey, value) => {
    setExportOptions((prev) => ({
      ...prev,
      [optionKey]: value,
    }));
  };

  const handleExport = async () => {
    // Cek apakah format yang dipilih tersedia untuk user
    const format = ExportFormats.find((f) => f.id === selectedFormat);
    if (format?.isPremium && !isUserPremium) {
      return; // Tidak boleh export format premium
    }

    await exportWithFormat(selectedFormat, exportOptions);
    setIsOpen(false);
  };

  const selectedFormatData = ExportFormats.find((f) => f.id === selectedFormat);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Export Desain</DialogTitle>
          <DialogDescription>
            Pilih format dan pengaturan untuk mengexport desain Anda
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div>
            <h3 className="text-sm font-medium mb-3">Pilih Format</h3>
            <div className="grid grid-cols-1 gap-2">
              {ExportFormats.map((format) => {
                const IconComponent = format.icon;
                const isFormatAvailable = availableFormats.some(
                  (f) => f.id === format.id
                );
                const isPremiumFormat = format.isPremium;

                return (
                  <div
                    key={format.id}
                    className={`p-3 border rounded-lg transition-all relative ${
                      selectedFormat === format.id
                        ? "border-purple-500 bg-purple-50"
                        : isFormatAvailable
                          ? "border-gray-200 hover:border-gray-300 cursor-pointer"
                          : "border-gray-100 bg-gray-50 cursor-not-allowed opacity-60"
                    }`}
                    onClick={() =>
                      isFormatAvailable && handleFormatChange(format.id)
                    }
                  >
                    <div className="flex items-center gap-3">
                      <IconComponent className="w-5 h-5" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{format.name}</span>
                          {isPremiumFormat && (
                            <Badge
                              variant="secondary"
                              className="text-xs bg-purple-100 text-purple-700"
                            >
                              <Crown className="w-3 h-3 mr-1" />
                              PRO
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          {format.description}
                        </div>
                      </div>
                      {!isFormatAvailable && (
                        <Lock className="w-4 h-4 text-gray-400" />
                      )}
                    </div>

                    {/* Premium Upgrade Overlay */}
                    {isPremiumFormat && !isUserPremium && (
                      <div className="absolute inset-0 bg-white/80 rounded-lg flex items-center justify-center">
                        <Link href="/workspace/billing">
                          <Button
                            size="sm"
                            className="bg-purple-600 hover:bg-purple-700"
                          >
                            <Crown className="w-3 h-3 mr-1" />
                            Upgrade
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Format Options */}
          {selectedFormatData &&
            Object.keys(selectedFormatData.options).length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-3">Pengaturan</h3>
                <div className="space-y-3">
                  {Object.entries(selectedFormatData.options).map(
                    ([optionKey, optionValues]) => (
                      <div key={optionKey}>
                        <label className="text-sm font-medium capitalize mb-2 block">
                          {optionKey === "quality"
                            ? "Resolusi"
                            : optionKey === "preserveTransparency"
                              ? "Transparansi"
                              : optionKey === "format"
                                ? "Ukuran Kertas"
                                : optionKey === "orientation"
                                  ? "Orientasi"
                                  : optionKey}
                        </label>
                        {optionKey === "quality" && (
                          <p className="text-xs text-gray-500 mb-2">
                            Resolusi yang lebih tinggi menghasilkan file yang
                            lebih besar tetapi lebih tajam
                          </p>
                        )}
                        <select
                          value={
                            exportOptions[optionKey] || optionValues[0]?.value
                          }
                          onChange={(e) => {
                            const value =
                              optionKey === "preserveTransparency"
                                ? e.target.value === "true"
                                : e.target.value;
                            handleOptionChange(optionKey, value);
                          }}
                          className="w-full p-2 border border-gray-300 rounded-md text-sm"
                        >
                          {optionValues.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

          {/* Export Button */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isExporting}
            >
              Batal
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || !canvasEditor}
              className="min-w-[120px]"
            >
              {isExporting ? (
                <Loader2Icon className="w-4 h-4 animate-spin" />
              ) : (
                <>
                  <Download className="w-4 h-4" />
                  Export {selectedFormatData?.name}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ExportOptionsDialog;

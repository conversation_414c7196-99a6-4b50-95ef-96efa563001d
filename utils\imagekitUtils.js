/**
 * Utility functions untuk ImageKit operations
 * Mengelola upload, delete, dan cleanup assets
 */

import ImageKit from "imagekit";

/**
 * Create ImageKit instance dengan environment variables
 * @returns {ImageKit} ImageKit instance
 */
export const createImageKitInstance = () => {
  return new ImageKit({
    publicKey: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY,
    privateKey: process.env.NEXT_PUBLIC_IMAGEKIT_PRIVATE_KEY,
    urlEndpoint: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT,
  });
};

/**
 * Extract file ID dari ImageKit URL
 * @param {string} imageUrl - URL ImageKit
 * @returns {string|null} File ID atau null jika tidak ditemukan
 */
export const extractFileIdFromUrl = (imageUrl) => {
  if (!imageUrl || typeof imageUrl !== "string") {
    return null;
  }

  try {
    // ImageKit URL pattern: https://ik.imagekit.io/your_imagekit_id/path/filename.ext
    // File ID biasanya ada di path atau bisa diambil dari filename

    // Method 1: Extract dari filename jika menggunakan pattern designId_timestamp
    const urlParts = imageUrl.split("/");
    const filename = urlParts[urlParts.length - 1];

    if (filename) {
      // Remove extension
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
      return nameWithoutExt;
    }

    return null;
  } catch (error) {
    console.error("Error extracting file ID from URL:", error);
    return null;
  }
};

/**
 * Extract design ID dari ImageKit filename
 * @param {string} imageUrl - URL ImageKit
 * @returns {string|null} Design ID atau null jika tidak ditemukan
 */
export const extractDesignIdFromUrl = (imageUrl) => {
  if (!imageUrl || typeof imageUrl !== "string") {
    return null;
  }

  try {
    // Remove query parameters first (?updatedAt=xxx)
    const urlWithoutQuery = imageUrl.split("?")[0];
    const urlParts = urlWithoutQuery.split("/");
    const filename = urlParts[urlParts.length - 1];

    console.log(`Processing URL: ${imageUrl}`);
    console.log(`Filename extracted: ${filename}`);

    if (filename) {
      // Remove extension (.png, .jpg, etc)
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
      console.log(`Filename without extension: ${nameWithoutExt}`);

      // Pattern: designId_timestamp.png
      // Example: j57838na3xdc3668rmdm3esnp17grrq8_1748474739568
      const parts = nameWithoutExt.split("_");
      console.log(`Split parts:`, parts);

      if (parts.length >= 2) {
        // Return everything except the last part (timestamp)
        const designId = parts.slice(0, -1).join("_");
        console.log(`Extracted design ID: ${designId}`);
        return designId;
      } else {
        // Fallback: return the whole filename without extension
        console.log(
          `Fallback: using full filename as design ID: ${nameWithoutExt}`
        );
        return nameWithoutExt;
      }
    }

    return null;
  } catch (error) {
    console.error("Error extracting design ID from URL:", error);
    return null;
  }
};

/**
 * Delete file dari ImageKit berdasarkan URL
 * @param {string} imageUrl - URL ImageKit yang akan dihapus
 * @returns {Promise<boolean>} Success status
 */
export const deleteImageKitFile = async (imageUrl) => {
  if (!imageUrl) {
    console.warn("No image URL provided for deletion");
    return false;
  }

  try {
    const imagekit = createImageKitInstance();

    // Extract exact filename dari URL
    const urlWithoutQuery = imageUrl.split("?")[0];
    const urlParts = urlWithoutQuery.split("/");
    const exactFilename = urlParts[urlParts.length - 1];

    console.log(`Attempting to delete exact file: ${exactFilename}`);
    console.log(`From URL: ${imageUrl}`);

    // Method 1: Cari file berdasarkan exact filename
    let files = await imagekit.listFiles({
      searchQuery: `name="${exactFilename}"`,
      limit: 5,
    });

    console.log(`Found ${files.length} files with exact filename search`);

    // Method 2: Jika tidak ditemukan, coba dengan design ID pattern
    if (files.length === 0) {
      const designId = extractDesignIdFromUrl(imageUrl);
      if (designId) {
        console.log(`Trying pattern search with design ID: ${designId}`);
        files = await imagekit.listFiles({
          searchQuery: `name:${designId}*`,
          limit: 10,
        });
        console.log(`Found ${files.length} files with pattern search`);
      }
    }

    // Method 3: Jika masih tidak ditemukan, coba list semua files dan filter manual
    if (files.length === 0) {
      console.log("Trying manual search through all files...");
      const allFiles = await imagekit.listFiles({
        limit: 100, // Increase limit untuk manual search
      });

      files = allFiles.filter(
        (file) =>
          file.name === exactFilename ||
          file.name.includes(exactFilename.split(".")[0])
      );

      console.log(`Found ${files.length} files with manual search`);
    }

    if (files.length === 0) {
      console.warn(`No files found for deletion. URL: ${imageUrl}`);
      return false;
    }

    // Delete semua files yang ditemukan
    let deletedCount = 0;
    for (const file of files) {
      try {
        console.log(`Attempting to delete: ${file.name} (ID: ${file.fileId})`);
        await imagekit.deleteFile(file.fileId);
        console.log(`✅ Successfully deleted: ${file.name}`);
        deletedCount++;
      } catch (deleteError) {
        console.error(`❌ Failed to delete file ${file.name}:`, deleteError);
      }
    }

    console.log(
      `Deletion completed: ${deletedCount}/${files.length} files deleted`
    );
    return deletedCount > 0;
  } catch (error) {
    console.error("Error deleting ImageKit file:", error);
    return false;
  }
};

/**
 * Delete multiple ImageKit files berdasarkan design IDs
 * @param {string[]} imageUrls - Array of ImageKit URLs
 * @returns {Promise<{success: number, failed: number}>} Deletion results
 */
export const deleteMultipleImageKitFiles = async (imageUrls) => {
  if (!imageUrls || !Array.isArray(imageUrls)) {
    return { success: 0, failed: 0 };
  }

  let successCount = 0;
  let failedCount = 0;

  for (const url of imageUrls) {
    try {
      const success = await deleteImageKitFile(url);
      if (success) {
        successCount++;
      } else {
        failedCount++;
      }
    } catch (error) {
      console.error("Error in batch deletion:", error);
      failedCount++;
    }
  }

  return { success: successCount, failed: failedCount };
};

/**
 * Cleanup orphaned ImageKit files untuk design tertentu
 * @param {string} designId - Design ID untuk cleanup
 * @returns {Promise<number>} Jumlah files yang dihapus
 */
export const cleanupDesignAssets = async (designId) => {
  if (!designId) {
    console.warn("No design ID provided for cleanup");
    return 0;
  }

  try {
    const imagekit = createImageKitInstance();

    // Search for all files dengan design ID pattern
    const searchQuery = `name:${designId}*`;
    console.log("Cleaning up assets for design:", designId);

    const files = await imagekit.listFiles({
      searchQuery: searchQuery,
      limit: 20, // Increase limit untuk cleanup
    });

    console.log(
      `Found ${files.length} assets to cleanup for design ${designId}`
    );

    let deletedCount = 0;
    for (const file of files) {
      try {
        await imagekit.deleteFile(file.fileId);
        console.log(`Cleaned up asset: ${file.name}`);
        deletedCount++;
      } catch (deleteError) {
        console.error(`Failed to cleanup asset ${file.name}:`, deleteError);
      }
    }

    console.log(`Cleanup completed: ${deletedCount} assets removed`);
    return deletedCount;
  } catch (error) {
    console.error("Error during asset cleanup:", error);
    return 0;
  }
};

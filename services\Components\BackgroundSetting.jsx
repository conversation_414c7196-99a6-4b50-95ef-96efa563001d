import { useState } from "react";

import { useCanvasHook } from "@/hooks/useCanvas";

import ColorPickerEditor from "../Sharable/ColorPickerEditor";
import TransparencyButton from "../Sharable/TransparencyButton";

function BackgroundSetting() {
  const [bgColor, setBgColor] = useState("#fff");
  const { canvasEditor } = useCanvasHook();

  /**
   * Gunakan untuk mengubah warna background
   * @param {*} color
   */
  const onColorChange = (color) => {
    setBgColor(color);

    // Handle transparansi untuk background
    if (
      color === "transparent" ||
      (color.includes("rgba") && color.includes("0)"))
    ) {
      // Jika transparan, set background menjadi null/transparent
      canvasEditor?.set({
        backgroundColor: "transparent",
        backgroundImage: null,
      });
    } else {
      canvasEditor?.set({
        backgroundColor: color,
        backgroundImage: null,
      });
    }
    canvasEditor.renderAll();
  };

  return (
    <div className="space-y-3">
      <div className="flex justify-center">
        <TransparencyButton type="background" />
      </div>
      <ColorPickerEditor
        value={bgColor}
        onColorChange={(v) => onColorChange(v)}
        enableAlpha={true}
      />
    </div>
  );
}

export default BackgroundSetting;

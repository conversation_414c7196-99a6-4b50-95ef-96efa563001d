import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  ChevronLeft,
  ChevronRight,
  X,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  ChevronUp,
  ChevronDown,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { useCanvasHook } from "@/hooks/useCanvas";
import { useMobile } from "@/hooks/useMobile";

function LayerPanel({ isMinimized = false, onMinimize, isMobile = false }) {
  const { canvasEditor } = useCanvasHook();
  const [objects, setObjects] = useState([]);
  const [selectedObjectId, setSelectedObjectId] = useState(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const isMobileDevice = useMobile();

  // Update objects list when canvas changes
  useEffect(() => {
    if (!canvasEditor) return;

    const updateObjectsList = () => {
      const canvasObjects = canvasEditor.getObjects();
      const objectsInfo = canvasObjects.map((obj, index) => {
        let name = obj.name || obj.id || `Object ${index + 1}`;

        // Determine object type for display
        let type = obj.type;
        if (type === "i-text" || type === "textbox") {
          type = "text";
          name = obj.text ? `"${obj.text.substring(0, 20)}..."` : "Text";
        } else if (type === "image") {
          name = "Image";
        } else if (type === "rect") {
          name = "Rectangle";
        } else if (type === "circle") {
          name = "Circle";
        } else if (type === "triangle") {
          name = "Triangle";
        } else if (type === "line") {
          name = "Line";
        } else if (type === "group") {
          name = `Group (${obj.getObjects().length} items)`;
        }

        return {
          id: obj.id || `obj-${index}`,
          name,
          type,
          object: obj,
          isLocked: obj.selectable === false,
          isVisible: obj.visible !== false,
          zIndex: canvasObjects.length - index, // Higher index = front
        };
      });

      setObjects(objectsInfo);
    };

    // Initial update
    updateObjectsList();

    // Listen for canvas changes
    const handleObjectAdded = () => updateObjectsList();
    const handleObjectRemoved = () => updateObjectsList();
    const handleSelectionCreated = (e) => {
      const activeObject = e.selected?.[0];
      if (activeObject) {
        setSelectedObjectId(activeObject.id || null);
      }
    };
    const handleSelectionCleared = () => {
      setSelectedObjectId(null);
    };

    canvasEditor.on("object:added", handleObjectAdded);
    canvasEditor.on("object:removed", handleObjectRemoved);
    canvasEditor.on("selection:created", handleSelectionCreated);
    canvasEditor.on("selection:cleared", handleSelectionCleared);

    return () => {
      canvasEditor.off("object:added", handleObjectAdded);
      canvasEditor.off("object:removed", handleObjectRemoved);
      canvasEditor.off("selection:created", handleSelectionCreated);
      canvasEditor.off("selection:cleared", handleSelectionCleared);
    };
  }, [canvasEditor]);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleMinimize = () => {
    console.log(
      "LayerPanel toggleMinimize clicked, current isMinimized:",
      isMinimized
    );
    if (onMinimize) {
      onMinimize(!isMinimized);
      console.log("LayerPanel onMinimize called with:", !isMinimized);
    }
  };

  const selectObject = (objectInfo) => {
    if (!canvasEditor || objectInfo.isLocked) return;

    canvasEditor.setActiveObject(objectInfo.object);
    canvasEditor.requestRenderAll();
    setSelectedObjectId(objectInfo.id);
  };

  const toggleLock = (objectInfo, e) => {
    e.stopPropagation();
    if (!canvasEditor) return;

    const newLockState = !objectInfo.isLocked;
    objectInfo.object.set({
      selectable: !newLockState,
      evented: !newLockState,
    });

    // If locking the currently selected object, clear selection
    if (newLockState && selectedObjectId === objectInfo.id) {
      canvasEditor.discardActiveObject();
      setSelectedObjectId(null);
    }

    canvasEditor.requestRenderAll();
    console.log(
      `Object ${objectInfo.name} ${newLockState ? "locked" : "unlocked"}`
    );
  };

  const toggleVisibility = (objectInfo, e) => {
    e.stopPropagation();
    if (!canvasEditor) return;

    const newVisibility = !objectInfo.isVisible;
    objectInfo.object.set({
      visible: newVisibility,
    });

    // If hiding the currently selected object, clear selection
    if (!newVisibility && selectedObjectId === objectInfo.id) {
      canvasEditor.discardActiveObject();
      setSelectedObjectId(null);
    }

    canvasEditor.requestRenderAll();
    console.log(
      `Object ${objectInfo.name} ${newVisibility ? "shown" : "hidden"}`
    );
  };

  const moveLayer = (objectInfo, direction, e) => {
    e.stopPropagation();
    if (!canvasEditor) return;

    const objects = canvasEditor.getObjects();
    const currentIndex = objects.indexOf(objectInfo.object);

    if (direction === "up" && currentIndex < objects.length - 1) {
      // Move forward (higher z-index)
      canvasEditor.bringForward(objectInfo.object);
    } else if (direction === "down" && currentIndex > 0) {
      // Move backward (lower z-index)
      canvasEditor.sendBackwards(objectInfo.object);
    }

    canvasEditor.requestRenderAll();
    console.log(`Object ${objectInfo.name} moved ${direction}`);
  };

  // Don't render if minimized - akan ditangani oleh FloatingToggle
  if (isMinimized) {
    return null;
  }

  // Mobile layout
  if (isMobile || isMobileDevice) {
    return (
      <div className="w-full h-full bg-white flex flex-col">
        {/* Mobile Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            <span className="font-medium">
              Layers {objects.length > 0 && `(${objects.length})`}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMinimize}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Mobile Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {!canvasEditor || objects.length === 0 ? (
            <div className="text-sm text-gray-500 text-center py-8">
              Tidak ada object di canvas
            </div>
          ) : (
            <div className="space-y-2">
              {objects.map((objectInfo) => (
                <div
                  key={objectInfo.id}
                  className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                    selectedObjectId === objectInfo.id
                      ? "bg-blue-50 border border-blue-200"
                      : "hover:bg-gray-50"
                  } ${objectInfo.isLocked ? "opacity-60" : ""} ${
                    !objectInfo.isVisible ? "opacity-40" : ""
                  }`}
                >
                  {/* Object Info */}
                  <div
                    className="flex-1 min-w-0 cursor-pointer"
                    onClick={() => selectObject(objectInfo)}
                  >
                    <div className="text-sm font-medium truncate">
                      {objectInfo.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {objectInfo.type}
                    </div>
                  </div>

                  {/* Mobile Controls */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => toggleVisibility(objectInfo, e)}
                    >
                      {objectInfo.isVisible ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <EyeOff className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => toggleLock(objectInfo, e)}
                    >
                      {objectInfo.isLocked ? (
                        <Lock className="h-4 w-4" />
                      ) : (
                        <Unlock className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`fixed top-36 right-4 z-[100] bg-white border border-gray-200 rounded-lg shadow-lg transition-all duration-300 ${
        isCollapsed ? "w-16" : "w-80"
      } max-h-[calc(100vh-150px)] flex flex-col`}
    >
      {/* Header */}
      <div
        className={`flex items-center border-b border-gray-200 ${
          isCollapsed ? "flex-col p-2 gap-1" : "justify-between p-4"
        }`}
      >
        {!isCollapsed && (
          <div className="flex items-center gap-2">
            <Layers className="h-4 w-4" />
            <span className="font-medium">
              Layers {objects.length > 0 && `(${objects.length})`}
            </span>
          </div>
        )}

        {isCollapsed && (
          <div className="flex items-center justify-center w-full">
            <Layers className="h-4 w-4" />
          </div>
        )}

        <div
          className={`flex items-center gap-1 ${isCollapsed ? "flex-col" : ""}`}
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleCollapse}
            className="h-6 w-6 p-0"
            title={isCollapsed ? "Expand" : "Collapse"}
          >
            {isCollapsed ? (
              <ChevronLeft className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMinimize}
            className="h-6 w-6 p-0"
            title="Minimize"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      {!isCollapsed && (
        <div className="flex-1 overflow-y-auto p-4">
          {!canvasEditor || objects.length === 0 ? (
            <div className="text-sm text-gray-500 text-center py-8">
              Tidak ada object di canvas
            </div>
          ) : (
            <div className="space-y-1">
              {objects.map((objectInfo) => (
                <div
                  key={objectInfo.id}
                  className={`flex items-center gap-2 p-2 rounded transition-colors ${
                    selectedObjectId === objectInfo.id
                      ? "bg-blue-50 border border-blue-200"
                      : "hover:bg-gray-50"
                  } ${objectInfo.isLocked ? "opacity-60" : ""} ${
                    !objectInfo.isVisible ? "opacity-40" : ""
                  }`}
                >
                  {/* Object Type Icon & Name */}
                  <div
                    className="flex-1 min-w-0 cursor-pointer"
                    onClick={() => selectObject(objectInfo)}
                  >
                    <div className="text-sm font-medium truncate">
                      {objectInfo.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {objectInfo.type}
                    </div>
                  </div>

                  {/* Layer Controls */}
                  <div className="flex items-center gap-1">
                    {/* Visibility Toggle */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e) => toggleVisibility(objectInfo, e)}
                      title={objectInfo.isVisible ? "Hide" : "Show"}
                    >
                      {objectInfo.isVisible ? (
                        <Eye className="h-3 w-3" />
                      ) : (
                        <EyeOff className="h-3 w-3" />
                      )}
                    </Button>

                    {/* Lock Toggle */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e) => toggleLock(objectInfo, e)}
                      title={objectInfo.isLocked ? "Unlock" : "Lock"}
                    >
                      {objectInfo.isLocked ? (
                        <Lock className="h-3 w-3" />
                      ) : (
                        <Unlock className="h-3 w-3" />
                      )}
                    </Button>

                    {/* Layer Order Controls */}
                    <div className="flex flex-col">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-3 w-6 p-0"
                        onClick={(e) => moveLayer(objectInfo, "up", e)}
                        title="Bring Forward"
                        disabled={objectInfo.zIndex === objects.length}
                      >
                        <ChevronUp className="h-2 w-2" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-3 w-6 p-0"
                        onClick={(e) => moveLayer(objectInfo, "down", e)}
                        title="Send Backward"
                        disabled={objectInfo.zIndex === 1}
                      >
                        <ChevronDown className="h-2 w-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Collapsed View - Object Indicators */}
      {isCollapsed && (
        <div className="flex-1 overflow-y-auto p-2">
          {objects.length === 0 ? (
            <div className="text-center py-4">
              <div className="w-8 h-8 bg-gray-200 rounded mx-auto"></div>
            </div>
          ) : (
            <div className="space-y-2 flex flex-col items-center">
              {objects.map((objectInfo) => (
                <div
                  key={objectInfo.id}
                  className={`relative w-10 h-8 rounded cursor-pointer transition-colors flex items-center justify-center ${
                    selectedObjectId === objectInfo.id
                      ? "bg-blue-500"
                      : "bg-gray-300 hover:bg-gray-400"
                  } ${objectInfo.isLocked ? "opacity-60" : ""} ${
                    !objectInfo.isVisible ? "opacity-30" : ""
                  }`}
                  onClick={() => selectObject(objectInfo)}
                  title={`${objectInfo.name} (${objectInfo.type})${
                    objectInfo.isLocked ? " - Locked" : ""
                  }${!objectInfo.isVisible ? " - Hidden" : ""}`}
                >
                  {/* Object Type Indicator */}
                  <div className="text-xs font-bold text-white">
                    {objectInfo.type === "text"
                      ? "T"
                      : objectInfo.type === "image"
                        ? "I"
                        : objectInfo.type === "rect"
                          ? "R"
                          : objectInfo.type === "circle"
                            ? "C"
                            : objectInfo.type === "triangle"
                              ? "△"
                              : objectInfo.type === "line"
                                ? "—"
                                : objectInfo.type === "group"
                                  ? "G"
                                  : "?"}
                  </div>

                  {/* Status Indicators */}
                  {objectInfo.isLocked && (
                    <Lock className="absolute top-0 right-0 h-2 w-2 text-red-500 bg-white rounded-full" />
                  )}
                  {!objectInfo.isVisible && (
                    <EyeOff className="absolute bottom-0 left-0 h-2 w-2 text-gray-600 bg-white rounded-full" />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default LayerPanel;

/**
 * Debug utilities untuk ImageKit operations
 * Membantu troubleshoot masalah delete dan search
 */

import { createImageKitInstance } from "./imagekitUtils";

/**
 * List semua files di ImageKit untuk debugging
 * @param {number} limit - Limit jumlah files
 * @returns {Promise<Array>} List of files
 */
export const listAllImageKitFiles = async (limit = 50) => {
  try {
    const imagekit = createImageKitInstance();

    console.log(`Fetching ${limit} files from ImageKit...`);
    const files = await imagekit.listFiles({
      limit: limit,
    });

    console.log(`Found ${files.length} files:`);
    files.forEach((file, index) => {
      console.log(`${index + 1}. ${file.name} (ID: ${file.fileId})`);
      console.log(`   URL: ${file.url}`);
      console.log(`   Size: ${file.size} bytes`);
      console.log(`   Created: ${new Date(file.createdAt).toLocaleString()}`);
      console.log("---");
    });

    return files;
  } catch (error) {
    console.error("Error listing ImageKit files:", error);
    return [];
  }
};

/**
 * Search files berdasarkan query untuk debugging
 * @param {string} searchQuery - Query untuk search
 * @returns {Promise<Array>} Search results
 */
export const searchImageKitFiles = async (searchQuery) => {
  try {
    const imagekit = createImageKitInstance();

    console.log(`Searching ImageKit files with query: ${searchQuery}`);
    const files = await imagekit.listFiles({
      searchQuery: searchQuery,
      limit: 20,
    });

    console.log(`Search results: ${files.length} files found`);
    files.forEach((file, index) => {
      console.log(`${index + 1}. ${file.name} (ID: ${file.fileId})`);
    });

    return files;
  } catch (error) {
    console.error("Error searching ImageKit files:", error);
    return [];
  }
};

/**
 * Test delete operation untuk specific file
 * @param {string} filename - Nama file untuk test delete
 * @returns {Promise<boolean>} Success status
 */
export const testDeleteImageKitFile = async (filename) => {
  try {
    const imagekit = createImageKitInstance();

    console.log(`Testing delete for file: ${filename}`);

    // Cari file berdasarkan nama
    const files = await imagekit.listFiles({
      searchQuery: `name="${filename}"`,
      limit: 5,
    });

    if (files.length === 0) {
      console.log(`File not found: ${filename}`);
      return false;
    }

    console.log(`Found ${files.length} matching files:`);
    files.forEach((file) => {
      console.log(`- ${file.name} (ID: ${file.fileId})`);
    });

    // Test delete first file
    const fileToDelete = files[0];
    console.log(`Attempting to delete: ${fileToDelete.name}`);

    await imagekit.deleteFile(fileToDelete.fileId);
    console.log(`✅ Successfully deleted: ${fileToDelete.name}`);

    return true;
  } catch (error) {
    console.error("Error in test delete:", error);
    return false;
  }
};

/**
 * Debug URL parsing untuk troubleshoot extraction
 * @param {string} imageUrl - URL untuk debug
 * @returns {Object} Parsed information
 */
export const debugUrlParsing = (imageUrl) => {
  console.log(`\n=== DEBUG URL PARSING ===`);
  console.log(`Original URL: ${imageUrl}`);

  // Remove query parameters
  const urlWithoutQuery = imageUrl.split("?")[0];
  console.log(`URL without query: ${urlWithoutQuery}`);

  // Extract filename
  const urlParts = urlWithoutQuery.split("/");
  const filename = urlParts[urlParts.length - 1];
  console.log(`Extracted filename: ${filename}`);

  // Remove extension
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
  console.log(`Filename without extension: ${nameWithoutExt}`);

  // Split by underscore
  const parts = nameWithoutExt.split("_");
  console.log(`Split parts:`, parts);

  // Extract design ID
  let designId = null;
  if (parts.length >= 2) {
    designId = parts.slice(0, -1).join("_");
    console.log(`Extracted design ID: ${designId}`);
  } else {
    designId = nameWithoutExt;
    console.log(`Fallback design ID: ${designId}`);
  }

  console.log(`=== END DEBUG ===\n`);

  return {
    originalUrl: imageUrl,
    urlWithoutQuery,
    filename,
    nameWithoutExt,
    parts,
    designId,
  };
};

/**
 * Test complete delete flow untuk debugging
 * @param {string} imageUrl - URL untuk test
 * @returns {Promise<boolean>} Success status
 */
export const testCompleteDeleteFlow = async (imageUrl) => {
  console.log(`\n=== TESTING COMPLETE DELETE FLOW ===`);

  // Step 1: Debug URL parsing
  const parsed = debugUrlParsing(imageUrl);

  // Step 2: Test different search methods
  const imagekit = createImageKitInstance();

  console.log(`\n--- Testing Search Methods ---`);

  // Method 1: Exact filename search
  console.log(`1. Exact filename search: name="${parsed.filename}"`);
  let files1 = await searchImageKitFiles(`name="${parsed.filename}"`);

  // Method 2: Design ID pattern search
  console.log(`2. Design ID pattern search: name:${parsed.designId}*`);
  let files2 = await searchImageKitFiles(`name:${parsed.designId}*`);

  // Method 3: Partial filename search
  console.log(`3. Partial filename search: name:${parsed.nameWithoutExt}*`);
  let files3 = await searchImageKitFiles(`name:${parsed.nameWithoutExt}*`);

  // Step 3: Manual search through all files
  console.log(`\n--- Manual Search ---`);
  const allFiles = await listAllImageKitFiles(100);
  const manualMatches = allFiles.filter(
    (file) =>
      file.name === parsed.filename ||
      file.name.includes(parsed.nameWithoutExt) ||
      file.name.includes(parsed.designId)
  );

  console.log(`Manual search found ${manualMatches.length} matching files:`);
  manualMatches.forEach((file) => {
    console.log(`- ${file.name} (ID: ${file.fileId})`);
  });

  console.log(`\n=== END TEST ===\n`);

  return manualMatches.length > 0;
};

/**
 * Utility untuk cleanup test files
 * @param {string} pattern - Pattern untuk cleanup
 * @returns {Promise<number>} Jumlah files yang dihapus
 */
export const cleanupTestFiles = async (pattern = "test_") => {
  try {
    const imagekit = createImageKitInstance();

    console.log(`Cleaning up test files with pattern: ${pattern}`);
    const files = await imagekit.listFiles({
      searchQuery: `name:${pattern}*`,
      limit: 50,
    });

    console.log(`Found ${files.length} test files to cleanup`);

    let deletedCount = 0;
    for (const file of files) {
      try {
        await imagekit.deleteFile(file.fileId);
        console.log(`Deleted test file: ${file.name}`);
        deletedCount++;
      } catch (error) {
        console.error(`Failed to delete test file ${file.name}:`, error);
      }
    }

    console.log(`Cleanup completed: ${deletedCount} test files deleted`);
    return deletedCount;
  } catch (error) {
    console.error("Error during test cleanup:", error);
    return 0;
  }
};

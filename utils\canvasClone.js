/**
 * Simplified canvas object cloning utilities
 * <PERSON><PERSON><PERSON><PERSON> masalah dengan fabric.js clone method yang kompleks
 */

/**
 * Clone object dengan pendekatan yang sangat sederhana
 * @param {fabric.Canvas} canvas - Fabric.js canvas instance
 * @returns {Promise<boolean>} - Success status
 */
export const simpleCloneObject = async (canvas) => {
  if (!canvas) {
    console.warn("Canvas tidak tersedia");
    return false;
  }

  const activeObject = canvas.getActiveObject();
  if (!activeObject) {
    console.warn("Tidak ada object yang dipilih");
    return false;
  }

  try {
    // Import fabric classes
    const fabric = await import("fabric");

    let newObject = null;

    // Buat object baru berdasarkan type dengan cara yang paling sederhana
    switch (activeObject.type) {
      case "rect":
        newObject = new fabric.Rect({
          left: activeObject.left + 10,
          top: activeObject.top + 10,
          width: activeObject.width,
          height: activeObject.height,
          fill: activeObject.fill,
          stroke: activeObject.stroke,
          strokeWidth: activeObject.strokeWidth,
          scaleX: activeObject.scaleX,
          scaleY: activeObject.scaleY,
          angle: activeObject.angle,
          id: `rect-${Date.now()}`,
          name: activeObject.name
            ? `${activeObject.name} - Copy`
            : "Rectangle Copy",
        });
        break;

      case "circle":
        newObject = new fabric.Circle({
          left: activeObject.left + 10,
          top: activeObject.top + 10,
          radius: activeObject.radius,
          fill: activeObject.fill,
          stroke: activeObject.stroke,
          strokeWidth: activeObject.strokeWidth,
          scaleX: activeObject.scaleX,
          scaleY: activeObject.scaleY,
          angle: activeObject.angle,
          id: `circle-${Date.now()}`,
          name: activeObject.name
            ? `${activeObject.name} - Copy`
            : "Circle Copy",
        });
        break;

      case "triangle":
        newObject = new fabric.Triangle({
          left: activeObject.left + 10,
          top: activeObject.top + 10,
          width: activeObject.width,
          height: activeObject.height,
          fill: activeObject.fill,
          stroke: activeObject.stroke,
          strokeWidth: activeObject.strokeWidth,
          scaleX: activeObject.scaleX,
          scaleY: activeObject.scaleY,
          angle: activeObject.angle,
          id: `triangle-${Date.now()}`,
          name: activeObject.name
            ? `${activeObject.name} - Copy`
            : "Triangle Copy",
        });
        break;

      case "line":
        newObject = new fabric.Line(
          [
            activeObject.x1 || 0,
            activeObject.y1 || 0,
            activeObject.x2 || 100,
            activeObject.y2 || 100,
          ],
          {
            left: activeObject.left + 10,
            top: activeObject.top + 10,
            stroke: activeObject.stroke,
            strokeWidth: activeObject.strokeWidth,
            scaleX: activeObject.scaleX,
            scaleY: activeObject.scaleY,
            angle: activeObject.angle,
            id: `line-${Date.now()}`,
            name: activeObject.name
              ? `${activeObject.name} - Copy`
              : "Line Copy",
          }
        );
        break;

      case "i-text":
        newObject = new fabric.IText(activeObject.text || "Text", {
          left: activeObject.left + 10,
          top: activeObject.top + 10,
          fontSize: activeObject.fontSize,
          fontFamily: activeObject.fontFamily,
          fontWeight: activeObject.fontWeight,
          fill: activeObject.fill,
          stroke: activeObject.stroke,
          strokeWidth: activeObject.strokeWidth,
          scaleX: activeObject.scaleX,
          scaleY: activeObject.scaleY,
          angle: activeObject.angle,
          id: `text-${Date.now()}`,
          name: activeObject.name ? `${activeObject.name} - Copy` : "Text Copy",
        });
        break;

      case "textbox":
        newObject = new fabric.Textbox(activeObject.text || "Text", {
          left: activeObject.left + 10,
          top: activeObject.top + 10,
          width: activeObject.width,
          fontSize: activeObject.fontSize,
          fontFamily: activeObject.fontFamily,
          fontWeight: activeObject.fontWeight,
          fill: activeObject.fill,
          stroke: activeObject.stroke,
          strokeWidth: activeObject.strokeWidth,
          scaleX: activeObject.scaleX,
          scaleY: activeObject.scaleY,
          angle: activeObject.angle,
          id: `textbox-${Date.now()}`,
          name: activeObject.name
            ? `${activeObject.name} - Copy`
            : "Textbox Copy",
        });
        break;

      default:
        console.warn(
          `Object type '${activeObject.type}' tidak didukung untuk clone sederhana`
        );
        return false;
    }

    if (newObject) {
      canvas.add(newObject);
      canvas.setActiveObject(newObject);
      canvas.renderAll();
      console.log(`Successfully cloned ${activeObject.type} object`);
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error in simple clone:", error);
    return false;
  }
};

/**
 * Clone object dengan fallback ke method sederhana
 * @param {fabric.Canvas} canvas - Fabric.js canvas instance
 * @returns {Promise<boolean>} - Success status
 */
export const safeCloneObject = async (canvas) => {
  // Coba method sederhana terlebih dahulu
  const success = await simpleCloneObject(canvas);

  if (success) {
    return true;
  }

  console.warn("Simple clone gagal, tidak ada fallback method");
  return false;
};

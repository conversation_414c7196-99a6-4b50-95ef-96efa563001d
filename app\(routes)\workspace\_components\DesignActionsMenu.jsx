"use client";

import { toast } from "sonner";
import { useState } from "react";
import { useMutation } from "convex/react";
import { MoreVertical, Edit, Trash2, Copy } from "lucide-react";

import { api } from "@/convex/_generated/api";
import { deleteImageKitFile } from "@/utils/imagekitUtils";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

function DesignActionsMenu({ design, onDesignDeleted, onDesignDuplicated }) {
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newName, setNewName] = useState(design?.name || "");
  const [isLoading, setIsLoading] = useState(false);

  const RenameDesign = useMutation(api.designs.RenameDesign);
  const DeleteDesign = useMutation(api.designs.DeleteDesign);
  const DuplicateDesign = useMutation(api.designs.DuplicateDesign);

  /**
   * Handle rename design
   */
  const handleRename = async () => {
    if (!newName.trim() || newName === design?.name) {
      setIsRenameDialogOpen(false);
      return;
    }

    try {
      setIsLoading(true);
      await RenameDesign({
        id: design._id,
        name: newName.trim(),
      });
      toast.success("Nama desain berhasil diubah!");
      setIsRenameDialogOpen(false);
    } catch (error) {
      console.error("Error renaming design:", error);
      toast.error("Gagal mengubah nama desain!");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle delete design dengan cleanup ImageKit assets
   */
  const handleDelete = async () => {
    let deleteToastId;

    try {
      setIsLoading(true);

      deleteToastId = toast.loading(
        "Menghapus desain dan membersihkan assets...",
        {
          duration: Infinity,
        }
      );

      // Delete design dari database
      const result = await DeleteDesign({
        id: design._id,
      });

      // Cleanup ImageKit assets jika ada imagePreview
      if (result?.deletedDesign?.imagePreview) {
        console.log(
          "Cleaning up ImageKit assets for:",
          result.deletedDesign.name
        );

        try {
          const cleanupSuccess = await deleteImageKitFile(
            result.deletedDesign.imagePreview
          );
          if (cleanupSuccess) {
            console.log("ImageKit assets cleaned up successfully");
          } else {
            console.warn("ImageKit cleanup completed with warnings");
          }
        } catch (cleanupError) {
          console.error("Error cleaning up ImageKit assets:", cleanupError);
          // Don't fail the whole operation if cleanup fails
        }
      }

      toast.dismiss(deleteToastId);
      toast.success("Desain dan assets berhasil dihapus!");
      setIsDeleteDialogOpen(false);

      if (onDesignDeleted) {
        onDesignDeleted(design._id);
      }
    } catch (error) {
      console.error("Error deleting design:", error);

      if (deleteToastId) {
        toast.dismiss(deleteToastId);
      }

      toast.error("Gagal menghapus desain!");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle duplicate design
   */
  const handleDuplicate = async () => {
    try {
      setIsLoading(true);
      const result = await DuplicateDesign({
        id: design._id,
      });
      toast.success("Desain berhasil diduplikat!");
      if (onDesignDuplicated) {
        onDesignDuplicated(result);
      }
    } catch (error) {
      console.error("Error duplicating design:", error);
      toast.error(error.message || "Gagal menduplikat desain!");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Open rename dialog
   */
  const openRenameDialog = () => {
    setNewName(design?.name || "");
    setIsRenameDialogOpen(true);
  };

  /**
   * Handle key down in rename input
   */
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleRename();
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-gray-100"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={openRenameDialog}>
            <Edit className="mr-2 h-4 w-4" />
            Ubah Nama
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleDuplicate} disabled={isLoading}>
            <Copy className="mr-2 h-4 w-4" />
            {isLoading ? "Menduplikat..." : "Duplikat"}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Hapus
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Rename Dialog */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Ubah Nama Desain</DialogTitle>
            <DialogDescription>
              Masukkan nama baru untuk desain Anda.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Input
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Nama desain"
              disabled={isLoading}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRenameDialogOpen(false)}
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button
              onClick={handleRename}
              disabled={isLoading || !newName.trim()}
            >
              {isLoading ? "Menyimpan..." : "Simpan"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Hapus Desain</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus desain "{design?.name}"?
              Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              {isLoading ? "Menghapus..." : "Hapus"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default DesignActionsMenu;

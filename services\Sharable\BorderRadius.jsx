import { Slider } from "@/components/ui/slider";
import { useCanvasHook } from "@/hooks/useCanvas";

function BorderRadius() {
  const { canvasEditor } = useCanvasHook();

  const onRadiusChange = (value) => {
    const activeObject = canvasEditor.getActiveObject();
    if (activeObject) {
      activeObject.set({
        rx: value,
        ry: value,
        cornerRadius: value,
      });
      canvasEditor.renderAll();
    }
  };

  return (
    <div>
      <h2 className="my-2">Ubah Radius</h2>
      <Slider
        defaultValue={[0]}
        max={100}
        step={1}
        onValueChange={(v) => onRadiusChange(v[0])}
      />
    </div>
  );
}

export default BorderRadius;

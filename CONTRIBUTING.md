# 🤝 Panduan Kontribusi CanvaIn

Terima kasih atas minat Anda untuk berkontribusi pada CanvaIn! Ka<PERSON> menyambut semua bentuk kontribusi dari komunitas.

## 📋 Daftar Isi

- [Code of Conduct](#code-of-conduct)
- [Cara Berkontribusi](#cara-berkontribusi)
- [Setup Development](#setup-development)
- [Coding Standards](#coding-standards)
- [Commit Guidelines](#commit-guidelines)
- [Pull Request Process](#pull-request-process)

## Code of Conduct

Dengan berpartisipasi dalam proyek ini, Anda setuju untuk mematuhi [Code of Conduct](CODE_OF_CONDUCT.md) kami.

## Cara Berkontribusi

### 🐛 Melaporkan Bug

- Gunakan GitHub Issues untuk melaporkan bug
- Jelaskan langkah-langkah untuk mereproduksi bug
- Sertakan screenshot jika memungkinkan
- Cantumkan informasi environment (OS, browser, versi Node.js)

### 💡 Mengusulkan Fitur

- Buka GitHub Issue dengan label "feature request"
- Jelaskan fitur yang diinginkan dan alasannya
- Diskusikan implementasi yang mungkin

### 🔧 Kontribusi Kode

1. Fork repository
2. Buat branch untuk fitur/perbaikan Anda
3. Implementasikan perubahan
4. Tulis/update tests jika diperlukan
5. Pastikan semua tests pass
6. Submit Pull Request

## Setup Development

### Prasyarat

- Node.js 18.0+
- npm/yarn/pnpm
- Git

### Langkah Setup

```bash
# Clone repository
git clone https://github.com/inuldev/canvain.git
cd canvain

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env.local
# Edit .env.local dengan konfigurasi Anda

# Setup Convex
npx convex dev

# Jalankan development server
npm run dev
```

## Coding Standards

### JavaScript/React

- Gunakan functional components dengan hooks
- Ikuti ESLint configuration yang ada
- Gunakan camelCase untuk variabel dan functions
- Gunakan PascalCase untuk components
- Tambahkan JSDoc comments untuk functions yang kompleks

### CSS/Styling

- Gunakan TailwindCSS untuk styling
- Ikuti utility-first approach
- Gunakan responsive design principles
- Hindari inline styles kecuali untuk dynamic values
- **Mobile-First Approach**: Design untuk mobile terlebih dahulu
- **Responsive Breakpoints**: Mobile (≤768px), Tablet (769-1024px), Desktop (>1024px)
- **Touch-Friendly**: Minimum 44px untuk touch targets

### File Structure

- Komponen dalam folder `components/`
- Pages dalam folder `app/`
- Utilities dalam folder `lib/`
- Hooks dalam folder `hooks/`
- Context dalam folder `context/`

## Commit Guidelines

Gunakan format commit message yang konsisten:

```
type(scope): description

[optional body]

[optional footer]
```

### Types

- `feat`: Fitur baru
- `fix`: Bug fix
- `docs`: Perubahan dokumentasi
- `style`: Perubahan formatting/styling
- `refactor`: Refactoring kode
- `test`: Menambah/mengubah tests
- `chore`: Maintenance tasks

### Contoh

```
feat(canvas): add shape rotation functionality

- Add rotation handles to shapes
- Implement rotation logic in fabric.js
- Update shape settings component

Closes #123
```

## Pull Request Process

1. **Update Documentation**: Pastikan README dan dokumentasi lain up-to-date
2. **Add Tests**: Tambahkan tests untuk fitur baru
3. **Check Build**: Pastikan build berhasil tanpa error
4. **Review Ready**: Pastikan kode siap untuk review

### PR Template

```markdown
## Deskripsi

Jelaskan perubahan yang dibuat

## Jenis Perubahan

- [ ] Bug fix
- [ ] Fitur baru
- [ ] Breaking change
- [ ] Dokumentasi

## Testing

- [ ] Tests pass
- [ ] Manual testing dilakukan
- [ ] Cross-browser testing (jika diperlukan)
- [ ] **Mobile Testing**: Test di real mobile devices
- [ ] **Responsive Testing**: Test di berbagai screen sizes
- [ ] **Touch Testing**: Verify touch interactions berfungsi dengan baik

## Screenshots

Tambahkan screenshot jika ada perubahan UI

## Checklist

- [ ] Kode mengikuti style guidelines
- [ ] Self-review sudah dilakukan
- [ ] Dokumentasi sudah diupdate
- [ ] Tests sudah ditambahkan
- [ ] Mobile responsiveness diverifikasi (jika ada perubahan UI)
- [ ] Touch interactions tested (jika ada perubahan canvas/UI)
```

## 🚀 Tips untuk Kontributor

### Untuk Pemula

- Mulai dengan issues yang berlabel "good first issue"
- Baca kode yang ada untuk memahami struktur
- Jangan ragu bertanya di discussions

### Untuk Kontributor Berpengalaman

- Bantu review PR dari kontributor lain
- Bantu improve dokumentasi
- Suggest architectural improvements

## 📞 Bantuan

Jika Anda membutuhkan bantuan:

- Buka GitHub Discussions
- Tag maintainers di issue/PR
- Join Discord server (jika ada)

---

Terima kasih telah berkontribusi pada CanvaIn! 🎨

import { useCanvasHook } from "@/hooks/useCanvas";

function MoveForward() {
  const { canvasEditor } = useCanvasHook();

  const MoveForward = () => {
    const activeObject = canvasEditor.getActiveObject();
    if (activeObject) {
      canvasEditor.bringObjectForward(activeObject);
    }
  };

  return (
    <div onClick={MoveForward} className="cursor-pointer">
      Tampak Depan
    </div>
  );
}

export default MoveForward;

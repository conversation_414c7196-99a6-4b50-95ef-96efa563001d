import Image from "next/image";
import { Circle, Line, Rect, Triangle } from "fabric";

import { useCanvasHook } from "@/hooks/useCanvas";

import Stickers from "./Stickers";
import { ShapeList } from "../Options";

function Shapes() {
  const { canvasEditor } = useCanvasHook();

  const onShapeSelect = (shape) => {
    const properties = {
      left: 100,
      top: 100,
      radius: 50,
      fill: "black",
      stroke: "black",
      width: 100,
      height: 100,
      id: `${shape.name.toLowerCase()}-${Date.now()}`,
      name: shape.name,
    };

    if (shape.name == "Circle") {
      const circleRef = new Circle({
        ...properties,
      });
      canvasEditor.add(circleRef);
    } else if (shape.name == "Square") {
      const squareRef = new Rect({
        ...properties,
      });
      canvasEditor.add(squareRef);
    } else if (shape.name == "Triangle") {
      const triangleRef = new Triangle({
        ...properties,
      });
      canvasEditor.add(triangleRef);
    } else if (shape.name == "Line") {
      const lineRef = new Line([50, 100, 200, 100], {
        stroke: "black",
        strokeWidth: 5,
        id: `line-${Date.now()}`,
        name: "Line",
      });
      canvasEditor.add(lineRef);
    }

    canvasEditor.renderAll();
  };

  return (
    <div>
      <div className="grid grid-cols-3 gap-3">
        {ShapeList.map((shape, index) => (
          <div
            className="p-2 border rounded-xl"
            key={index}
            onClick={() => onShapeSelect(shape)}
          >
            <Image src={shape.icon} alt={shape.name} width={100} height={100} />
          </div>
        ))}
      </div>

      <Stickers />
    </div>
  );
}

export default Shapes;

"use client";

import Image from "next/image";
import { toast } from "sonner";
import { useState, useEffect, useContext } from "react";
import {
  User,
  Shield,
  Bell,
  Monitor,
  Moon,
  Sun,
  Download,
  Trash2,
  Database,
  HardDrive,
} from "lucide-react";

import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { UserDetailContext } from "@/context/UserDetailContext";

function SettingsPage() {
  const { userDetail } = useContext(UserDetailContext);

  const [settings, setSettings] = useState({
    theme: "light",
    language: "id",
    notifications: {
      email: true,
      browser: true,
      updates: true,
      tips: false,
    },
    privacy: {
      profileVisible: true,
      designsPublic: false,
      analytics: true,
    },
    storage: {
      autoCleanup: true,
      cleanupDays: 30,
    },
  });

  const [storageUsage, setStorageUsage] = useState({
    used: 0,
    total: 1000, // MB
    designs: 0,
    images: 0,
  });

  // Load settings
  useEffect(() => {
    const savedSettings = localStorage.getItem("canvain-user-settings");
    if (savedSettings) {
      setSettings((prev) => ({ ...prev, ...JSON.parse(savedSettings) }));
    }

    // Simulate storage calculation
    calculateStorageUsage();
  }, []);

  const calculateStorageUsage = () => {
    // Simulate storage calculation
    const designs = Math.floor(Math.random() * 50) + 10;
    const images = Math.floor(Math.random() * 200) + 50;
    const used = designs * 2 + images * 0.5; // MB

    setStorageUsage({
      used: Math.round(used),
      total: 1000,
      designs,
      images,
    });
  };

  const saveSettings = (newSettings) => {
    setSettings(newSettings);
    localStorage.setItem("canvain-user-settings", JSON.stringify(newSettings));
    toast.success("Pengaturan disimpan!");
  };

  const updateSetting = (path, value) => {
    const newSettings = { ...settings };
    const keys = path.split(".");
    let current = newSettings;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    saveSettings(newSettings);
  };

  const exportData = () => {
    toast.success("Data export akan segera dimulai...");
    // Implement data export logic
  };

  const clearCache = () => {
    localStorage.removeItem("canvain-cache");
    toast.success("Cache berhasil dibersihkan!");
  };

  const deleteAccount = () => {
    if (
      confirm(
        "Apakah Anda yakin ingin menghapus akun? Tindakan ini tidak dapat dibatalkan."
      )
    ) {
      toast.error(
        "Fitur hapus akun belum tersedia. Hubungi support untuk bantuan."
      );
    }
  };

  return (
    <div className="p-10 w-full max-w-4xl mx-auto">
      {/* Header */}
      <div className="relative mb-10">
        <Image
          src={"/banner.png"}
          alt="banner"
          width={1800}
          height={300}
          className="w-full h-[200px] rounded-2xl object-cover"
        />
        <div className="absolute bottom-5 left-10 text-white">
          <h2 className="text-3xl font-bold">Pengaturan</h2>
          <p className="text-lg opacity-90">Kelola preferensi dan akun Anda</p>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Profile Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profil
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Nama</label>
              <Input value={userDetail?.name || ""} disabled />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Email</label>
              <Input value={userDetail?.email || ""} disabled />
            </div>
            <p className="text-xs text-gray-500">
              Untuk mengubah informasi profil, silakan login ulang dengan akun
              Google Anda.
            </p>
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Tampilan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Theme</label>
              <Select
                value={settings.theme}
                onValueChange={(value) => updateSetting("theme", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">
                    <div className="flex items-center gap-2">
                      <Sun className="h-4 w-4" />
                      Light
                    </div>
                  </SelectItem>
                  <SelectItem value="dark">
                    <div className="flex items-center gap-2">
                      <Moon className="h-4 w-4" />
                      Dark
                    </div>
                  </SelectItem>
                  <SelectItem value="system">
                    <div className="flex items-center gap-2">
                      <Monitor className="h-4 w-4" />
                      System
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Bahasa</label>
              <Select
                value={settings.language}
                onValueChange={(value) => updateSetting("language", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="id">🇮🇩 Bahasa Indonesia</SelectItem>
                  <SelectItem value="en">🇺🇸 English</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notifikasi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm">Email Notifications</label>
              <Switch
                checked={settings.notifications.email}
                onCheckedChange={(checked) =>
                  updateSetting("notifications.email", checked)
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm">Browser Notifications</label>
              <Switch
                checked={settings.notifications.browser}
                onCheckedChange={(checked) =>
                  updateSetting("notifications.browser", checked)
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm">Update Notifications</label>
              <Switch
                checked={settings.notifications.updates}
                onCheckedChange={(checked) =>
                  updateSetting("notifications.updates", checked)
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm">Tips & Tricks</label>
              <Switch
                checked={settings.notifications.tips}
                onCheckedChange={(checked) =>
                  updateSetting("notifications.tips", checked)
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Privacy */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Privasi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm">Profile Visible</label>
              <Switch
                checked={settings.privacy.profileVisible}
                onCheckedChange={(checked) =>
                  updateSetting("privacy.profileVisible", checked)
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm">Public Designs</label>
              <Switch
                checked={settings.privacy.designsPublic}
                onCheckedChange={(checked) =>
                  updateSetting("privacy.designsPublic", checked)
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm">Analytics</label>
              <Switch
                checked={settings.privacy.analytics}
                onCheckedChange={(checked) =>
                  updateSetting("privacy.analytics", checked)
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Storage */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HardDrive className="h-5 w-5" />
              Storage
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Used</span>
                <span>
                  {storageUsage.used} / {storageUsage.total} MB
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{
                    width: `${(storageUsage.used / storageUsage.total) * 100}%`,
                  }}
                ></div>
              </div>
            </div>

            <div className="text-xs text-gray-500 space-y-1">
              <div>• {storageUsage.designs} designs</div>
              <div>• {storageUsage.images} images</div>
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm">Auto Cleanup</label>
              <Switch
                checked={settings.storage.autoCleanup}
                onCheckedChange={(checked) =>
                  updateSetting("storage.autoCleanup", checked)
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Data
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={exportData} variant="outline" className="w-full">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>

            <Button onClick={clearCache} variant="outline" className="w-full">
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Cache
            </Button>

            <Separator />

            <Button
              onClick={deleteAccount}
              variant="destructive"
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Account
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default SettingsPage;

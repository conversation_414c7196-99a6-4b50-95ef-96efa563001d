# 🎨 CanvaIn - Platform Desain Grafis Modern

<div align="center">
  <img src="public/logo.png" alt="CanvaIn Logo" width="200"/>

  <p><strong>Platform desain grafis yang mudah digunakan untuk membuat poster, banner, presentasi, dan konten visual lainnya. Tanpa perlu keahlian desain!</strong></p>

[![Next.js](https://img.shields.io/badge/Next.js-15.3.0-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18.0.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![Fabric.js](https://img.shields.io/badge/Fabric.js-6.6.5-orange?style=flat-square)](http://fabricjs.com/)
[![Convex](https://img.shields.io/badge/Convex-1.24.1-purple?style=flat-square)](https://convex.dev/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-4.0-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)

</div>

## 📋 Daftar Isi

- [Tentang Proyek](#-tentang-proyek)
- [Fitur Utama](#-fitur-utama)
- [Teknologi yang Digunakan](#-teknologi-yang-digunakan)
- [Instalasi](#-instalasi)
- [Penggunaan](#-penggunaan)
- [Struktur Proyek](#-struktur-proyek)
- [API dan Database](#-api-dan-database)
- [Kontribusi](#-kontribusi)
- [Lisensi](#-lisensi)

## 🚀 Tentang Proyek

**Canvain** adalah platform desain grafis berbasis web yang terinspirasi dari Canva, dibangun dengan teknologi modern untuk memberikan pengalaman desain yang intuitif dan powerful. Platform ini memungkinkan pengguna untuk membuat berbagai jenis desain visual tanpa memerlukan keahlian desain grafis yang mendalam.

### 📚 Sumber Pembelajaran

Proyek ini dikembangkan berdasarkan tutorial dari **TubeGuruji** yang memberikan panduan lengkap untuk membangun Canva clone menggunakan teknologi modern:

> 📺 **[Build a Full Stack Canva Clone 2.0 with Next.js, React & Convex, ImageKit](https://www.youtube.com/watch?v=vjWyqs7eBP4)** > _oleh [TubeGuruji](https://www.youtube.com/@tubeguruji)_

Tutorial ini mencakup implementasi lengkap dari frontend hingga backend, termasuk autentifikasi, manajemen state, dan integrasi dengan berbagai layanan cloud.

### 🎯 Tujuan Proyek

- Menyediakan alternatif open-source untuk platform desain grafis komersial
- Memberikan pengalaman desain yang mudah dan intuitif
- Mendukung kolaborasi real-time antar pengguna
- Menyediakan template dan elemen desain yang beragam
- Menjadi referensi pembelajaran untuk developer yang ingin membangun aplikasi serupa

## ✨ Fitur Utama

### 🎨 Editor Desain

- **Drag & Drop Interface** - Interface yang intuitif untuk mendesain
- **Canvas Editor** - Editor berbasis Fabric.js yang powerful
- **Real-time Preview** - Melihat hasil desain secara langsung
- **Multi-layer Support** - Dukungan untuk multiple layer objek
- **📱 Mobile Responsive** - Optimized untuk desktop, tablet, dan mobile

### 📐 Tools & Elements

- **Shapes & Objects** - Berbagai bentuk geometris (lingkaran, persegi, segitiga, garis)
- **Text Editor** - Editor teks dengan berbagai font dan styling
- **Image Upload** - Upload dan manipulasi gambar
- **Color Picker** - Pemilih warna yang lengkap
- **Background Settings** - Pengaturan background yang fleksibel
- **Layer Management** - Lock, hide, reorder layers dengan mudah

### 📱 Mobile Experience

- **Touch-Friendly Interface** - Optimized untuk touch interactions
- **Mobile Navigation** - Hamburger menu dan bottom navigation
- **Gesture Support** - Pinch-to-zoom dan touch gestures
- **Responsive Layout** - Adaptive layout untuk semua screen sizes
- **Mobile Canvas** - Touch-optimized canvas dengan gesture controls

### 🖼️ Template & Assets

- **Template Library** - Koleksi template siap pakai
- **Image Search** - Integrasi pencarian gambar
- **Stickers & Icons** - Koleksi stiker dan ikon
- **Custom Elements** - Kemampuan menambah elemen custom

### 💾 Manajemen Proyek

- **Auto Save** - Penyimpanan otomatis desain
- **Project Management** - Manajemen proyek desain
- **Export Options** - Export ke berbagai format (PNG, SVG, PDF)
- **Cloud Storage** - Penyimpanan cloud dengan Convex

### 🔐 Autentifikasi & User Management

- **Stack Auth Integration** - Sistem autentifikasi yang aman
- **User Profiles** - Profil pengguna yang lengkap
- **Protected Routes** - Perlindungan route yang memerlukan login
- **Session Management** - Manajemen sesi yang robust

## 🛠️ Teknologi yang Digunakan

### Frontend

- **[Next.js 15.3.0](https://nextjs.org/)** - React framework dengan App Router
- **[React 18](https://reactjs.org/)** - Library UI yang powerful
- **[Fabric.js 6.6.5](http://fabricjs.com/)** - Canvas library untuk manipulasi grafis
- **[TailwindCSS 4.0](https://tailwindcss.com/)** - Utility-first CSS framework
- **[Radix UI](https://www.radix-ui.com/)** - Komponen UI yang accessible

### Backend & Database

- **[Convex](https://convex.dev/)** - Backend-as-a-Service dengan real-time sync
- **[Stack Auth](https://stack-auth.com/)** - Sistem autentifikasi modern

### UI Components & Styling

- **[Lucide React](https://lucide.dev/)** - Icon library yang modern
- **[React Color](https://casesandberg.github.io/react-color/)** - Color picker component
- **[Sonner](https://sonner.emilkowal.ski/)** - Toast notifications yang elegant

### Development Tools

- **[ESLint](https://eslint.org/)** - Linting untuk kualitas kode
- **[PostCSS](https://postcss.org/)** - CSS processing tool

## 🚀 Instalasi

### Prasyarat

- Node.js 18.0 atau lebih baru
- npm, yarn, pnpm, atau bun
- Akun Convex untuk backend
- Akun Stack Auth untuk autentifikasi

### Langkah Instalasi

1. **Clone repository**

   ```bash
   git clone https://github.com/inuldev/canvain.git
   cd canvain
   ```

2. **Install dependencies**

   ```bash
   npm install
   # atau
   yarn install
   # atau
   pnpm install
   ```

3. **Setup environment variables**

   Buat file `.env.local` dan tambahkan:

   ```env
   # Convex
   NEXT_PUBLIC_CONVEX_URL=your_convex_url

   # Stack Auth
   NEXT_PUBLIC_STACK_PROJECT_ID=your_stack_project_id
   NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=your_stack_client_key

   # ImageKit (optional)
   NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY=your_imagekit_public_key
   NEXT_PUBLIC_IMAGEKIT_PRIVATE_KEY=your_imagekit_private_key
   NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT=your_imagekit_endpoint
   ```

4. **Setup Convex**

   ```bash
   npx convex dev
   ```

5. **Jalankan development server**

   ```bash
   npm run dev
   ```

6. **Buka aplikasi**

   Akses [http://localhost:3000](http://localhost:3000) di browser Anda.

## 📖 Penggunaan

### Memulai Desain Baru

1. Klik tombol "Mulai Desain Gratis" di homepage
2. Login atau daftar akun baru
3. Pilih template atau buat canvas custom
4. Mulai mendesain dengan tools yang tersedia

### Menggunakan Editor

#### Desktop

- **Menambah Elemen**: Gunakan sidebar untuk menambah shapes, text, atau gambar
- **Edit Objek**: Klik objek untuk mengedit properties seperti warna, ukuran, posisi
- **Layer Management**: Gunakan panel layer di kanan untuk mengatur objek
- **Save & Export**: Gunakan tombol Save untuk menyimpan, Export untuk download

#### Mobile

- **Navigation**: Gunakan hamburger menu untuk akses tools
- **Touch Controls**: Touch dan drag untuk manipulasi objek
- **Pinch Zoom**: Gunakan gesture pinch untuk zoom in/out canvas
- **Layer Panel**: Akses layer panel melalui bottom navigation

### Manajemen Proyek

- **Workspace**: Akses semua proyek Anda di halaman workspace
- **Templates**: Jelajahi template yang tersedia
- **Recent Designs**: Lihat desain terbaru Anda

## 📁 Struktur Proyek

```
canvain/
├── app/                         # Next.js App Router
│   ├── (routes)/                # Route groups
│   │   ├── design/              # Design editor pages
│   │   └── workspace/           # Workspace pages
│   ├── handler/                 # Stack Auth handler
│   ├── globals.css              # Global styles
│   ├── layout.js                # Root layout
│   ├── page.js                  # Homepage
│   └── provider.jsx             # App providers
├── components/                  # Reusable UI components
│   └── ui/                      # Shadcn/ui components
├── context/                     # React contexts
│   ├── CanvasContext.jsx        # Canvas state management
│   └── UserDetailContext.jsx    # User data management
├── convex/                      # Convex backend
│   ├── designs.js               # Design CRUD operations
│   ├── schema.js                # Database schema
│   ├── templates.js             # Template operations
│   └── users.js                 # User operations
├── hooks/                       # Custom React hooks
│   ├── useCanvas.js             # Canvas hook
│   ├── useCanvasOperations.js   # Canvas operations hook
│   ├── useExport.js             # Export hook
│   └── useMobile.js             # Mobile detection hook
├── lib/                         # Utility functions
│   └── utils.js                 # Helper utilities
├── public/                      # Static assets
│   ├── logo.png                 # App logo
│   └── ...                      # Other images
├── services/                    # Business logic
│   ├── Components/              # Feature components
│   │   ├── MobileNavigation.jsx # Mobile navigation component
│   │   ├── LayerPanel.jsx       # Layer management panel
│   │   └── ...                  # Other components
│   ├── Options/                 # Configuration options
│   └── Sharable/                # Sharable components
├── docs/                        # Documentation
│   ├── MOBILE_RESPONSIVE.md     # Mobile responsiveness guide
│   └── TOGGLE_PANELS.md         # Panel toggle documentation
└── package.json                 # Dependencies
```

## 🗄️ API dan Database

### Database Schema (Convex)

#### Users Table

```javascript
users: {
  name: string,
  email: string,
  picture: string,
  subscriptionId?: string
}
```

#### Designs Table

```javascript
designs: {
  name: string,
  width: number,
  height: number,
  jsonTemplate?: any,
  imagePreview?: string,
  uid: Id<"users">
}
```

#### Templates Table

```javascript
templates: {
  name: string,
  imagePreview: string,
  jsonData: any,
  active: boolean,
  width?: number,
  height?: number
}
```

### API Endpoints

#### Designs

- `CreateNewDesign` - Membuat desain baru
- `GetDesign` - Mengambil data desain
- `SaveDesign` - Menyimpan desain
- `GetUserDesigns` - Mengambil semua desain user
- `CreateDesignFromTemplate` - Membuat desain dari template

#### Templates

- `GetAllTemplatest` - Mengambil semua template

#### Users

- `CreateNewUser` - Membuat user baru

## 🤝 Kontribusi

Kami menyambut kontribusi dari komunitas! Berikut cara berkontribusi:

1. **Fork** repository ini
2. **Buat branch** untuk fitur baru (`git checkout -b feature/AmazingFeature`)
3. **Commit** perubahan Anda (`git commit -m 'Add some AmazingFeature'`)
4. **Push** ke branch (`git push origin feature/AmazingFeature`)
5. **Buat Pull Request**

### Guidelines Kontribusi

- Ikuti coding standards yang ada
- Tambahkan tests untuk fitur baru
- Update dokumentasi jika diperlukan
- Gunakan commit message yang deskriptif

## 📝 Lisensi

Proyek ini dilisensikan di bawah [MIT License](LICENSE) - lihat file LICENSE untuk detail lengkap.

## 🙏 Acknowledgments

- [Canva](https://canva.com) - Inspirasi untuk UI/UX design
- [Fabric.js](http://fabricjs.com/) - Canvas manipulation library
- [Next.js](https://nextjs.org/) - React framework
- [Convex](https://convex.dev/) - Backend platform
- [Radix UI](https://www.radix-ui.com/) - UI components

---

<div align="center">
  <p>Dibuat dengan ❤️ untuk komunitas desainer Indonesia</p>
  <p>© 2025 CanvaIn. Semua hak dilindungi.</p>
</div>

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>_Sans,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Source_Sans_3,
  <PERSON><PERSON>_Di<PERSON>lay,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
} from "next/font/google";
import { StackProvider, StackTheme } from "@stackframe/stack";

import "./globals.css";
import { stackServerApp } from "../stack";
import ConvexClientProvider from "./ConvexClientProvider";

import { Toaster } from "@/components/ui/sonner";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Canva-inspired fonts
const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
});

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
});

const roboto = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["300", "400", "500", "700"],
});

const lato = Lato({
  variable: "--font-lato",
  subsets: ["latin"],
  weight: ["300", "400", "700"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const sourceSans = Source_Sans_3({
  variable: "--font-source-sans",
  subsets: ["latin"],
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
});

const merriweather = Merriweather({
  variable: "--font-merriweather",
  subsets: ["latin"],
  weight: ["300", "400", "700"],
});

const oswald = Oswald({
  variable: "--font-oswald",
  subsets: ["latin"],
});

const pacifico = Pacifico({
  variable: "--font-pacifico",
  subsets: ["latin"],
  weight: "400",
});

const dancingScript = Dancing_Script({
  variable: "--font-dancing-script",
  subsets: ["latin"],
});

export const metadata = {
  title: "CanvaIn",
  description: "Aplikasi untuk menggambar seperti canva",
};

export default function RootLayout({ children }) {
  return (
    <html lang="id">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${montserrat.variable} ${openSans.variable} ${roboto.variable} ${lato.variable} ${poppins.variable} ${sourceSans.variable} ${playfair.variable} ${merriweather.variable} ${oswald.variable} ${pacifico.variable} ${dancingScript.variable} antialiased`}
      >
        <StackProvider app={stackServerApp}>
          <StackTheme>
            <ConvexClientProvider>
              {children}
              <Toaster />
            </ConvexClientProvider>
          </StackTheme>
        </StackProvider>
      </body>
    </html>
  );
}

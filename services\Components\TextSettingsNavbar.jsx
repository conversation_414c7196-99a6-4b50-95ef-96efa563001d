import { useCanvasHook } from "@/hooks/useCanvas";
import ObjectToolbar from "./ObjectToolbar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { TextSettingsList } from "../Options";
import FontStyles from "../Sharable/FontStyles";

function TextSettingsNavbar() {
  const { canvasEditor } = useCanvasHook();

  return (
    <div className="flex gap-6 items-center">
      {TextSettingsList.map((shape, index) => (
        <div
          key={index}
          className="hover:scale-105 transition-all cursor-pointer"
        >
          <Popover>
            <PopoverTrigger asChild>
              <shape.icon />
            </PopoverTrigger>
            <PopoverContent>{shape.component}</PopoverContent>
          </Popover>
        </div>
      ))}

      <FontStyles />

      {/* Object Operations Toolbar */}
      <ObjectToolbar />
    </div>
  );
}

export default TextSettingsNavbar;

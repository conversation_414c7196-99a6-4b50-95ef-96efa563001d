"use client";

import Image from "next/image";
import { toast } from "sonner";
import { useContext } from "react";
import { useMutation } from "convex/react";
import { useRouter } from "next/navigation";

import { api } from "@/convex/_generated/api";
import { canvasSizeOptions } from "@/services/Options";
import { UserDetailContext } from "@/context/UserDetailContext";

function IntroOptions() {
  const router = useRouter();
  const { userDetail, isCreatingUser } = useContext(UserDetailContext);
  const createDesignRecord = useMutation(api.designs.CreateNewDesign);

  /**
   * Membuat desain baru dan simpan ke database
   * @param {*} option
   */
  const OnCanvasOptionSelect = async (option) => {
    // Validasi user sudah login dan data ter-load
    if (!userDetail?._id) {
      toast.error("Mohon tunggu, sedang memuat data user...");
      return;
    }

    try {
      toast("Membuat desain baru...");
      const result = await createDesignRecord({
        name: option.name,
        width: option.width,
        height: option.height,
        uid: userDetail._id,
      });

      router.push("/design/" + result);
    } catch (error) {
      toast.error(error.message || "Gagal membuat desain");
    }
  };

  return (
    <div>
      <div className="relative">
        <Image
          src={"/banner-home.png"}
          alt="banner"
          width={1800}
          height={300}
          className="w-full h-[200px] rounded-2xl object-cover"
        />
        <h2 className="text-3xl absolute bottom-5 left-10 text-white">
          Selamat Datang
        </h2>
      </div>
      <div className="flex gap-6 items-center mt-10 justify-center flex-wrap">
        {canvasSizeOptions.map((option, index) => (
          <div
            key={index}
            className={`flex flex-col items-center transition-all ${
              userDetail?._id && !isCreatingUser
                ? "cursor-pointer hover:scale-105"
                : "cursor-not-allowed opacity-50"
            }`}
            onClick={() =>
              userDetail?._id && !isCreatingUser && OnCanvasOptionSelect(option)
            }
          >
            <Image
              src={option.icon}
              alt={option.name}
              width={60}
              height={60}
              className="aspect-square w-[50px] h-[50px]"
            />
            <h2 className="text-xs mt-2 font-medium text-center">
              {option.name}
            </h2>
          </div>
        ))}
      </div>

      {/* Loading indicator untuk user data */}
      {(!userDetail?._id || isCreatingUser) && !userDetail?.error && (
        <div className="text-center mt-4 text-gray-500 text-sm">
          Memuat data user...
        </div>
      )}
    </div>
  );
}

export default IntroOptions;

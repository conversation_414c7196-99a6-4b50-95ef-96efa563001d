"use client";

import { CirclePlus } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

import { WorkspaceMenu } from "@/services/Options";

import CustomCanvasDialog from "./CustomCanvasDialog";

function Sidebar() {
  const path = usePathname();
  const router = useRouter();

  return (
    <div className="h-screen shadow-sm p-2 bg-purple-50">
      <CustomCanvasDialog>
        <div className="p-2 flex items-center flex-col hover:cursor-pointer mb-5">
          <CirclePlus className="bg-purple-600 text-white rounded-full h-8 w-8" />
          <h2 className="text-sm text-purple-600">Buat Baru</h2>
        </div>
      </CustomCanvasDialog>

      {WorkspaceMenu.map((menu, index) => (
        <div
          key={index}
          className={`p-2 flex items-center flex-col mb-4 relative group hover:bg-purple-100 rounded-xl cursor-pointer ${menu.path == path && "bg-purple-100"}`}
          onClick={() => router.push(menu.path)}
        >
          <menu.icon
            className={`group-hover:text-purple-800 ${menu.path == path && "bg-purple-100"}`}
            strokeWidth={1.5}
          />
          <h2
            className={`text-sm group-hover:text-purple-800 ${menu.path == path && "bg-purple-100"}`}
          >
            {menu.name}
          </h2>

          {/* Badge for Coming Soon */}
          {menu.badge && (
            <div
              className={`absolute -top-1 -right-1 ${menu.badgeColor || "bg-yellow-500"} text-white text-xs px-1.5 py-0.5 rounded-full`}
            >
              {menu.badge}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

export default Sidebar;

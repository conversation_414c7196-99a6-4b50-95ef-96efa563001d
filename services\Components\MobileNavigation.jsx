"use client";

import { useState } from "react";
import { Menu, X, Layers } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useMobile } from "@/hooks/useMobile";

/**
 * Komponen Mobile Navigation untuk hamburger menu dan bottom navigation
 */
function MobileNavigation({
  onSidebarToggle,
  onLayerToggle,
  objectCount = 0,
  showLayerButton = true,
}) {
  const isMobile = useMobile();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  if (!isMobile) return null;

  const handleSidebarToggle = () => {
    setIsMenuOpen(false);
    onSidebarToggle?.();
  };

  const handleLayerToggle = () => {
    onLayerToggle?.();
  };

  return (
    <>
      {/* Top Mobile Header */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSidebarToggle}
          className="p-2"
        >
          <Menu className="h-5 w-5" />
        </Button>

        <div className="text-sm font-medium text-gray-700">CanvaIn Editor</div>

        {showLayerButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLayerToggle}
            className="p-2 relative"
          >
            <Layers className="h-5 w-5" />
            {objectCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-purple-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {objectCount > 9 ? "9+" : objectCount}
              </span>
            )}
          </Button>
        )}
      </div>

      {/* Bottom Mobile Navigation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-around">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSidebarToggle}
            className="flex flex-col items-center gap-1 p-2"
          >
            <Menu className="h-4 w-4" />
            <span className="text-xs">Tools</span>
          </Button>

          {showLayerButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLayerToggle}
              className="flex flex-col items-center gap-1 p-2 relative"
            >
              <Layers className="h-4 w-4" />
              <span className="text-xs">Layers</span>
              {objectCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-purple-600 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {objectCount > 9 ? "9+" : objectCount}
                </span>
              )}
            </Button>
          )}
        </div>
      </div>
    </>
  );
}

export default MobileNavigation;

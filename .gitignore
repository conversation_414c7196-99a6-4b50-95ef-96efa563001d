# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env.local
.env.production
.env.development

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# convex
.convex/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# logs
logs
*.log

# runtime data
pids
*.pid
*.seed
*.pid.lock

# temporary folders
tmp/
temp/

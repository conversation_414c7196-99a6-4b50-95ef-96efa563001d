import { IText, Textbox } from "fabric";

import { useCanvasHook } from "@/hooks/useCanvas";

function TextSettings() {
  const { canvasEditor } = useCanvasHook();

  const onAddTextClick = (type) => {
    if (canvasEditor) {
      if (type == "Heading") {
        const textRef = new IText("Add Heading", {
          fontSize: 30,
          fontWeight: "bold",
          fontFamily: "Arial",
          fill: "black",
          left: 100,
          top: 100,
          id: `heading-${Date.now()}`,
          name: "Heading",
        });

        canvasEditor.add(textRef);
        canvasEditor.renderAll();
      } else if (type == "Subheading") {
        const textRef = new IText("Add Subheading", {
          fontSize: 20,
          fontWeight: "400",
          fontFamily: "Arial",
          fill: "black",
          left: 100,
          top: 100,
          id: `subheading-${Date.now()}`,
          name: "Subheading",
        });

        canvasEditor.add(textRef);
        canvasEditor.renderAll();
      } else {
        const textRef = new Textbox("Add Paragraph", {
          fontSize: 13,
          fontWeight: "normal",
          fontFamily: "Arial",
          fill: "black",
          left: 100,
          top: 100,
          id: `paragraph-${Date.now()}`,
          name: "Paragraph",
        });

        canvasEditor.add(textRef);
        canvasEditor.renderAll();
      }
    }
  };

  return (
    <div className="flex flex-col gap-3">
      <h2
        className="font-bold text-3xl p-3 bg-secondary rounded-xl cursor-pointer"
        onClick={() => onAddTextClick("Heading")}
      >
        Add Heading
      </h2>
      <h2
        className="font-medium text-xl p-3 bg-secondary rounded-xl cursor-pointer"
        onClick={() => onAddTextClick("Subheading")}
      >
        Add Subheading
      </h2>
      <h2
        className="text-md p-3 bg-secondary rounded-xl cursor-pointer"
        onClick={() => onAddTextClick("Para")}
      >
        Paragraph
      </h2>
    </div>
  );
}

export default TextSettings;

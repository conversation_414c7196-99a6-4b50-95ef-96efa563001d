import { FileImage, FileText, Image, Crown } from "lucide-react";

export const ExportFormats = [
  {
    id: "png",
    name: "PNG",
    description: "Format gambar dengan kualitas tinggi",
    icon: Image,
    extension: ".png",
    isPremium: false,
    options: {
      quality: [
        { label: "Rendah (0.5x)", value: 0.5 },
        { label: "Sedang (1x)", value: 1 },
        { label: "Tinggi (2x)", value: 2 },
        { label: "Sangat Tinggi (3x)", value: 3 },
      ],
      preserveTransparency: [
        { label: "Pertahankan Transparansi", value: true },
        { label: "Background Putih", value: false },
      ],
    },
  },
  {
    id: "svg",
    name: "SVG",
    description:
      "Format vektor yang dapat diperbesar tanpa kehilangan kualitas",
    icon: FileImage,
    extension: ".svg",
    isPremium: true,
    options: {},
  },
  {
    id: "pdf",
    name: "PDF",
    description: "Format dokumen untuk pencetakan dan berbagi",
    icon: FileText,
    extension: ".pdf",
    isPremium: true,
    options: {
      format: [
        { label: "A4", value: "a4" },
        { label: "A3", value: "a3" },
        { label: "Letter", value: "letter" },
        { label: "Legal", value: "legal" },
      ],
      orientation: [
        { label: "Portrait", value: "portrait" },
        { label: "Landscape", value: "landscape" },
      ],
    },
  },
];

export const getFormatById = (id) => {
  return ExportFormats.find((format) => format.id === id);
};

export const getDefaultOptions = (formatId) => {
  const format = getFormatById(formatId);
  if (!format) return {};

  const defaultOptions = {};

  if (format.options.quality) {
    defaultOptions.quality = format.options.quality[1].value; // Default to medium
  }

  if (format.options.preserveTransparency) {
    defaultOptions.preserveTransparency =
      format.options.preserveTransparency[0].value; // Default to preserve transparency
  }

  if (format.options.format) {
    defaultOptions.format = format.options.format[0].value; // Default to first option
  }

  if (format.options.orientation) {
    defaultOptions.orientation = format.options.orientation[0].value; // Default to first option
  }

  return defaultOptions;
};

/**
 * Get available export formats berdasarkan subscription type
 */
export const getAvailableFormats = (isPremium = false) => {
  if (isPremium) {
    return ExportFormats;
  }

  return ExportFormats.filter((format) => !format.isPremium);
};

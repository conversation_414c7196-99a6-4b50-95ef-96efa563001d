"use client";

import { Crown, Zap } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { getSubscriptionDisplayInfo } from "@/lib/subscription";

function SubscriptionBadge({ subscriptionType = "free", className = "" }) {
  const displayInfo = getSubscriptionDisplayInfo(subscriptionType);
  const isPremium = subscriptionType === "pro" || subscriptionType === "team";

  return (
    <Badge
      variant={isPremium ? "default" : "secondary"}
      className={`${isPremium ? "bg-purple-600 hover:bg-purple-700" : "bg-green-600 hover:bg-green-700"} ${className}`}
    >
      {isPremium ? (
        <Crown className="h-3 w-3 mr-1" />
      ) : (
        <Zap className="h-3 w-3 mr-1" />
      )}
      {displayInfo.badge}
    </Badge>
  );
}

export default SubscriptionBadge;

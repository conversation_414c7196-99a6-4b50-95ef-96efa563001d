# Fitur Duplikat - Dokumentasi

## 📋 Overview

Fitur duplikat telah berhasil diimplementasikan dalam aplikasi Canva clone ini. Fitur ini memungkinkan pengguna untuk menduplikat design dan object canvas dengan mudah.

## 🎯 Fitur yang Diimplementasikan

### 1. **Duplikat Design (Database Level)**

- Duplikat design lengkap dengan semua data
- Nama otomatis ditambahkan suffix "- Copy"
- Validasi limit untuk free users (maksimal 5 design)
- Refresh otomatis list design setelah duplikat

### 2. **Duplikat Canvas Object (Canvas Level)**

- Duplikat object yang sedang dipilih di canvas
- Support untuk single object dan multiple selection
- Posisi otomatis digeser (+10px left, +10px top)
- ID unik untuk setiap object yang diduplikat

## 🔧 Implementasi Teknis

### API Convex - DuplicateDesign

**File:** `convex/designs.js`

```javascript
export const DuplicateDesign = mutation({
  args: {
    id: v.id("designs"),
  },
  handler: async (ctx, args) => {
    // Validasi design exists
    const originalDesign = await ctx.db.get(args.id);

    // Validasi user dan limit
    const user = await ctx.db.get(originalDesign.uid);

    // Cek limit untuk free users
    if (userSubscriptionType === "free") {
      // Maksimal 5 design untuk free users
    }

    // Buat design baru dengan data yang sama
    const result = await ctx.db.insert("designs", {
      name: `${originalDesign.name} - Copy`,
      // ... copy semua data lainnya
    });

    return result;
  },
});
```

### Component Updates

**File:** `app/(routes)/workspace/_components/DesignActionsMenu.jsx`

- ✅ Tambah import `Copy` icon dari lucide-react
- ✅ Tambah `DuplicateDesign` mutation
- ✅ Tambah `handleDuplicate` function
- ✅ Tambah menu item "Duplikat" di dropdown
- ✅ Tambah prop `onDesignDuplicated` callback

**File:** `app/(routes)/workspace/_components/RecentDesign.jsx`

- ✅ Tambah `handleDesignDuplicated` callback
- ✅ Pass callback ke `DesignActionsMenu`
- ✅ Auto refresh list setelah duplikat

### Canvas Object Duplication

**File:** `utils/canvasClone.js` (New Implementation)

```javascript
export const simpleCloneObject = async (canvas) => {
  const activeObject = canvas.getActiveObject();

  // Buat object baru berdasarkan type dengan cara sederhana
  switch (activeObject.type) {
    case "rect":
      newObject = new fabric.Rect({
        left: activeObject.left + 10,
        top: activeObject.top + 10,
        width: activeObject.width,
        height: activeObject.height,
        fill: activeObject.fill,
        // ... copy properties lainnya
      });
      break;
    // ... cases lainnya
  }

  canvas.add(newObject);
  return true;
};
```

**File:** `hooks/useCanvasOperations.js`

- ✅ Import `safeCloneObject` utility
- ✅ Tambah `duplicateObject` function dengan error handling
- ✅ Toast notifications untuk feedback
- ✅ Support untuk basic shapes (rect, circle, triangle, line, text)

**File:** `app/(routes)/design/_components/CanvasEditor.jsx`

- ✅ Import `duplicateObject` dari useCanvasOperations
- ✅ Tambah keyboard shortcut `Ctrl+D` untuk duplikat
- ✅ Update dependency array di useEffect

## 🎮 Cara Penggunaan

### Duplikat Design

1. Buka halaman workspace
2. Hover pada design card
3. Klik menu titik tiga (⋮)
4. Pilih "Duplikat"
5. Design baru akan muncul dengan nama "Original Name - Copy"

### Duplikat Canvas Object

1. Buka design editor
2. Pilih object di canvas
3. Tekan `Ctrl+D` atau gunakan menu object toolbar
4. Object akan diduplikat dengan posisi sedikit bergeser

## 🔒 Validasi dan Limitasi

### Free User Limits

- Maksimal 5 design total
- Error message jika limit tercapai
- Suggestion untuk upgrade ke premium

### Error Handling

- Validasi design exists
- Validasi user exists
- Validasi canvas object exists
- Toast notifications untuk feedback

## 🧪 Testing

### Test Cases untuk Design Duplication

1. ✅ Duplikat design dengan data lengkap
2. ✅ Duplikat design dengan nama yang benar
3. ✅ Validasi limit free user
4. ✅ Refresh list setelah duplikat
5. ✅ Error handling untuk design tidak ditemukan

### Test Cases untuk Canvas Object Duplication

1. ✅ Duplikat single object
2. ✅ Duplikat multiple objects
3. ✅ Posisi object yang benar
4. ✅ ID unik untuk setiap object
5. ✅ Keyboard shortcut Ctrl+D

## 🔧 Perbaikan Error

### Masalah Awal

- Error: `TypeError: t is not iterable` saat clone object
- Fabric.js clone method tidak stabil untuk beberapa object type
- Kompleksitas dalam handling different object types

### Solusi Implementasi

1. **Simplified Clone Approach**: Buat object baru dengan constructor langsung
2. **Type-Specific Handling**: Handle setiap type object secara terpisah
3. **Error Prevention**: Avoid fabric.js clone method yang bermasalah
4. **Fallback Strategy**: Graceful degradation jika clone gagal

### File Baru

- `utils/canvasClone.js`: Implementasi clone yang lebih sederhana dan stabil
- Menggantikan `utils/canvasUtils.js` untuk object cloning

## 📝 Catatan Pengembangan

- Implementasi menggunakan constructor langsung untuk stabilitas
- Menghindari fabric.js clone method yang kompleks dan bermasalah
- Focus pada user experience yang sederhana
- Konsisten dengan pattern yang ada di aplikasi
- Support terbatas pada basic shapes untuk menghindari error

## 🚀 Future Enhancements

- [ ] Duplikat dengan custom offset position
- [ ] Batch duplicate multiple designs
- [ ] Duplicate design ke workspace lain
- [ ] Smart naming untuk duplikat berulang
- [ ] Undo/Redo support untuk canvas duplication

## 🔗 Related Features

- **ImageKit Asset Cleanup**: Otomatis hapus assets saat design dihapus
  - Dokumentasi: `docs/IMAGEKIT_CLEANUP.md`
  - Optimasi storage dan biaya ImageKit
  - Cleanup otomatis saat delete design

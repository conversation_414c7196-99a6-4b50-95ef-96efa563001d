import axios from "axios";
import Image from "next/image";
import { FabricImage } from "fabric";
import { <PERSON>I<PERSON>, Loader, AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useCanvasHook } from "@/hooks/useCanvas";
import { toast } from "sonner";

function SearchImages() {
  const { canvasEditor } = useCanvasHook();
  const [imageList, setImageList] = useState([]);
  const [searchInput, setSearchInput] = useState();
  const [searchLoading, setSearchLoading] = useState(false);
  const [addingToCanvas, setAddingToCanvas] = useState(null); // Track which image is being added
  const [error, setError] = useState(null);

  useEffect(() => {
    GetImageList("Gradient");
  }, []);

  const GetImageList = async (searchInput) => {
    let searchToastId;

    try {
      setSearchLoading(true);
      setError(null);

      if (!searchInput || searchInput.trim() === "") {
        toast.error("Masukkan kata kunci pencarian");
        return;
      }

      searchToastId = toast.loading(`Mencari gambar "${searchInput}"...`, {
        duration: Infinity,
      });

      const result = await axios.get("https://api.unsplash.com/search/photos", {
        params: {
          query: searchInput,
          page: 1,
          per_page: 20,
        },
        headers: {
          Authorization:
            `Client-ID ` + process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY,
        },
        timeout: 10000, // 10 detik timeout
      });

      toast.dismiss(searchToastId);

      if (result?.data?.results?.length === 0) {
        toast.info(`Tidak ada gambar ditemukan untuk "${searchInput}"`);
        setImageList([]);
      } else {
        setImageList(result?.data?.results);
        toast.success(
          `Ditemukan ${result?.data?.results?.length} gambar untuk "${searchInput}"`,
          {
            duration: 3000,
          }
        );
      }
    } catch (error) {
      console.error("Search error:", error);

      if (searchToastId) {
        toast.dismiss(searchToastId);
      }

      setError("Gagal mencari gambar. Periksa koneksi internet Anda.");
      toast.error("Gagal mencari gambar", { duration: 5000 });
    } finally {
      setSearchLoading(false);
    }
  };

  /**
   * Gunakan untuk menambahkan gambar ke canvas
   */
  const addImageToCanvas = async (imageUrl, imageIndex) => {
    let canvasToastId;

    try {
      setAddingToCanvas(imageIndex);

      canvasToastId = toast.loading("Menambahkan gambar ke canvas...", {
        duration: Infinity,
      });

      const canvasImageRef = await FabricImage.fromURL(imageUrl, {
        crossOrigin: "anonymous",
        timeout: 15000, // 15 detik timeout
      });

      canvasImageRef.set({
        scaleX: 0.1,
        scaleY: 0.1,
        id: `image-${Date.now()}`,
        name: "Search Image",
      });

      canvasEditor.add(canvasImageRef);
      canvasEditor.renderAll();

      toast.dismiss(canvasToastId);
      toast.success("Gambar berhasil ditambahkan ke canvas", {
        duration: 3000,
      });
    } catch (error) {
      console.error("Canvas error:", error);

      if (canvasToastId) {
        toast.dismiss(canvasToastId);
      }

      toast.error("Gagal menambahkan gambar ke canvas", { duration: 5000 });
    } finally {
      setAddingToCanvas(null);
    }
  };

  return (
    <div className="mt-5">
      <h2 className="font-bold">Cari Gambar</h2>

      {/* Search Input */}
      <div className="flex gap-2 items-center my-2">
        <Input
          placeholder="Mountain, Nature, City..."
          onChange={(e) => setSearchInput(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === "Enter" && !searchLoading) {
              GetImageList(searchInput);
            }
          }}
          disabled={searchLoading}
        />
        <Button
          onClick={() => GetImageList(searchInput)}
          disabled={searchLoading}
          size="sm"
        >
          {searchLoading ? (
            <Loader className="animate-spin h-4 w-4" />
          ) : (
            <SearchIcon className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4 flex items-center gap-2">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-700">{error}</span>
        </div>
      )}

      {/* Loading State */}
      {searchLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-gray-600">
            <Loader className="animate-spin h-5 w-5" />
            <span>Mencari gambar...</span>
          </div>
        </div>
      )}

      {/* Image Grid */}
      {!searchLoading && (
        <div className="mt-3 grid grid-cols-2 gap-2 overflow-y-auto overflow-x-hidden h-[60vh]">
          {imageList.length === 0 && !error ? (
            <div className="col-span-2 text-center py-8 text-gray-500">
              <SearchIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Cari gambar dari Unsplash</p>
            </div>
          ) : (
            imageList.map((image, index) => (
              <div
                key={index}
                onClick={() => {
                  if (addingToCanvas !== index) {
                    addImageToCanvas(image?.urls?.full, index);
                  }
                }}
                className={`relative cursor-pointer transition-opacity duration-200 hover:opacity-80 ${
                  addingToCanvas === index
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                {/* Loading Overlay */}
                {addingToCanvas === index && (
                  <div className="absolute inset-0 bg-black/20 rounded-sm flex items-center justify-center z-10">
                    <Loader className="animate-spin h-4 w-4 text-white" />
                  </div>
                )}

                <Image
                  src={image?.urls?.thumb}
                  alt={image?.slug || `Image ${index + 1}`}
                  width={300}
                  height={300}
                  className="w-full h-[60px] rounded-sm object-cover"
                />

                {/* Image Info */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-1">
                  <p className="text-white text-xs truncate">
                    {image?.user?.name || "Unsplash"}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
}

export default SearchImages;

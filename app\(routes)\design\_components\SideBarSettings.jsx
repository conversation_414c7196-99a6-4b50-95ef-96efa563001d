import React from "react";
import { X } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

function SideBarSettings({ selectedOption, onClose }) {
  return (
    selectedOption && (
      <div className="w-[280px] p-5 min-h-full border-r bg-white transition-all duration-300 ease-in-out">
        {/* Header with Close Button */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="font-bold">{selectedOption?.name}</h2>
            <p className="text-sm text-gray-500">{selectedOption?.desc}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 hover:bg-gray-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="mt-3">{selectedOption?.component}</div>
      </div>
    )
  );
}

export default SideBarSettings;

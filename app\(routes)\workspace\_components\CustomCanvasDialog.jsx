import Link from "next/link";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useContext, useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { Loader2Icon, Crown, AlertCircle } from "lucide-react";

import { api } from "@/convex/_generated/api";
import { UserDetailContext } from "@/context/UserDetailContext";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { getRemainingDesigns, FREE_LIMITS } from "@/lib/subscription";

function CustomCanvasDialog({ children }) {
  const router = useRouter();
  const [name, setName] = useState();
  const [width, setWidth] = useState();
  const [height, setHeight] = useState();
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { userDetail, isCreatingUser } = useContext(UserDetailContext);
  const createDesignRecord = useMutation(api.designs.CreateNewDesign);

  // Get user subscription info
  const subscriptionInfo = useQuery(
    api.users.GetUserSubscriptionInfo,
    userDetail?._id ? { userId: userDetail._id } : "skip"
  );

  // Get user design count
  const designCount = useQuery(
    api.designs.GetUserDesignCount,
    userDetail?._id ? { uid: userDetail._id } : "skip"
  );

  const isPremium = subscriptionInfo?.isPremium || false;
  const remainingDesigns =
    designCount !== undefined && designCount !== null
      ? getRemainingDesigns(
          subscriptionInfo?.subscriptionType || "free",
          designCount || 0
        )
      : FREE_LIMITS.MAX_DESIGNS; // Default untuk user baru

  /**
   * Membuat desain baru dan simpan ke database
   */
  const onCreate = async () => {
    // Validasi user sudah login dan data ter-load
    if (!userDetail?._id) {
      toast.error("Mohon tunggu, sedang memuat data user...");
      return;
    }

    // Cek limit untuk free users
    if (!isPremium && remainingDesigns <= 0) {
      toast.error(
        "Limit desain gratis tercapai! Upgrade ke Pro untuk membuat lebih banyak desain."
      );
      return;
    }

    if (!name || !width || !height) {
      toast.error("Mohon lengkapi semua field");
      return;
    }

    try {
      toast("Membuat desain baru...");
      setLoading(true);
      const result = await createDesignRecord({
        name: name,
        width: Number(width),
        height: Number(height),
        uid: userDetail._id,
      });

      setLoading(false);
      setIsOpen(false);
      router.push("/design/" + result);
    } catch (error) {
      setLoading(false);
      toast.error(error.message || "Gagal membuat desain");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Buat Desain Baru</DialogTitle>
          <DialogDescription asChild>
            <div>
              <h2 className="text-sm">
                Buat desain baru dengan mengisi nama dan ukuran desain yang
                diinginkan
              </h2>

              {/* Limit Warning untuk Free Users */}
              {!isPremium && remainingDesigns <= 1 && (
                <Alert className="border-amber-200 bg-amber-50 mt-4">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800">
                    {remainingDesigns === 0 ? (
                      <div>
                        <strong>Limit desain tercapai!</strong> Anda telah
                        mencapai batas maksimal {FREE_LIMITS.MAX_DESIGNS} desain
                        gratis.{" "}
                        <Link
                          href="/workspace/billing"
                          className="underline font-medium"
                        >
                          Upgrade ke Pro
                        </Link>{" "}
                        untuk membuat desain unlimited.
                      </div>
                    ) : (
                      <div>
                        <strong>Tersisa {remainingDesigns} desain lagi!</strong>{" "}
                        Setelah ini Anda perlu{" "}
                        <Link
                          href="/workspace/billing"
                          className="underline font-medium"
                        >
                          upgrade ke Pro
                        </Link>{" "}
                        untuk melanjutkan.
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              <div className="mt-5">
                <label>Nama Desain</label>
                <Input
                  placeholder="Nama Desain"
                  onChange={(e) => setName(e.target.value)}
                />
                <div className="mt-1 flex gap-5 w-full">
                  <div className="w-full">
                    <label>Lebar</label>
                    <Input
                      className="mt-1"
                      type="number"
                      placeholder={500}
                      onChange={(e) => setWidth(e.target.value)}
                    />
                  </div>
                  <div className="w-full">
                    <label>Tinggi</label>
                    <Input
                      className="mt-1"
                      type="number"
                      placeholder={500}
                      onChange={(e) => setHeight(e.target.value)}
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-6">
                {!isPremium && remainingDesigns <= 0 ? (
                  <Link href="/workspace/billing" className="w-full">
                    <Button className="w-full bg-purple-600 hover:bg-purple-700">
                      <Crown className="h-4 w-4 mr-2" />
                      Upgrade ke Pro
                    </Button>
                  </Link>
                ) : (
                  <Button
                    className="w-full"
                    disabled={
                      loading ||
                      !name ||
                      !width ||
                      !height ||
                      !userDetail?._id ||
                      isCreatingUser
                    }
                    onClick={onCreate}
                  >
                    {loading ? (
                      <Loader2Icon className="animate-spin" />
                    ) : isCreatingUser || !userDetail?._id ? (
                      "Memuat data user..."
                    ) : (
                      "Buat Desain"
                    )}
                  </Button>
                )}
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

export default CustomCanvasDialog;

# ImageKit Asset Cleanup - Dokumentasi

## 📋 Overview

Fitur optimasi untuk menghapus assets ImageKit secara otomatis ketika design dihapus. Ini membantu mengurangi penggunaan storage yang tidak perlu dan mengoptimalkan biaya ImageKit.

## 🎯 Fitur yang Diimplementasikan

### 1. **Automatic Asset Cleanup**

- Hapus ImageKit assets otomatis saat design dihapus
- Cleanup berdasarkan design ID pattern
- Error handling yang robust
- Toast notifications untuk feedback

### 2. **Batch Cleanup Support**

- Support untuk cleanup multiple designs
- Batch operations untuk efisiensi
- Progress tracking dan reporting

### 3. **Manual Cleanup Tools**

- Cleanup berdasarkan design ID
- Utility functions untuk maintenance
- Hook untuk easy integration

## 🔧 Implementasi Teknis

### ImageKit Utils

**File:** `utils/imagekitUtils.js`

```javascript
// Extract design ID dari ImageKit URL
export const extractDesignIdFromUrl = (imageUrl) => {
  // Pattern: designId_timestamp.png
  const filename = urlParts[urlParts.length - 1];
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
  const parts = nameWithoutExt.split("_");
  return parts.slice(0, -1).join("_"); // Remove timestamp
};

// Delete ImageKit file berdasarkan URL
export const deleteImageKitFile = async (imageUrl) => {
  const imagekit = createImageKitInstance();
  const designId = extractDesignIdFromUrl(imageUrl);

  // Search dan delete semua files dengan pattern designId
  const files = await imagekit.listFiles({
    searchQuery: `name:${designId}*`,
  });

  for (const file of files) {
    await imagekit.deleteFile(file.fileId);
  }
};
```

### Updated Delete Design API

**File:** `convex/designs.js`

```javascript
export const DeleteDesign = mutation({
  handler: async (ctx, args) => {
    const design = await ctx.db.get(args.id);

    // Simpan info untuk cleanup
    const designInfo = {
      id: args.id,
      imagePreview: design.imagePreview,
      name: design.name,
    };

    await ctx.db.delete(args.id);

    // Return info untuk cleanup di client
    return {
      success: true,
      deletedDesign: designInfo,
    };
  },
});
```

### Enhanced Delete Handler

**File:** `app/(routes)/workspace/_components/DesignActionsMenu.jsx`

```javascript
const handleDelete = async () => {
  // Delete dari database
  const result = await DeleteDesign({ id: design._id });

  // Cleanup ImageKit assets
  if (result?.deletedDesign?.imagePreview) {
    await deleteImageKitFile(result.deletedDesign.imagePreview);
  }

  toast.success("Desain dan assets berhasil dihapus!");
};
```

### Custom Hook untuk Cleanup

**File:** `hooks/useImageKitCleanup.js`

```javascript
export const useImageKitCleanup = () => {
  const cleanupSingleDesign = useCallback(async (imageUrl, designName) => {
    return await deleteImageKitFile(imageUrl);
  });

  const cleanupWithToast = useCallback(async (imageUrl, designName) => {
    // Cleanup dengan toast notifications
  });

  return { cleanupSingleDesign, cleanupWithToast };
};
```

## 🔍 Asset Pattern Recognition

### File Naming Convention

ImageKit files menggunakan pattern:

```
{designId}_{timestamp}.png
```

Contoh:

```
k17abc123_1703123456789.png
k17def456_1703123456790.png
```

### Search Query Pattern

```javascript
// Cari semua files untuk design tertentu
const searchQuery = `name:${designId}*`;

// Contoh hasil:
// - k17abc123_1703123456789.png (design preview)
// - k17abc123_1703123456790.png (uploaded image)
// - k17abc123_1703123456791.png (AI generated image)
```

## 🛡️ Error Handling

### Graceful Degradation

```javascript
try {
  // Delete dari database (prioritas utama)
  const result = await DeleteDesign({ id: design._id });

  // Cleanup assets (secondary operation)
  if (result?.deletedDesign?.imagePreview) {
    try {
      await deleteImageKitFile(result.deletedDesign.imagePreview);
    } catch (cleanupError) {
      // Log error tapi jangan gagalkan operasi utama
      console.error("Cleanup failed:", cleanupError);
    }
  }
} catch (error) {
  // Handle database delete error
  toast.error("Gagal menghapus desain!");
}
```

### Validation Checks

- ✅ Validasi ImageKit credentials
- ✅ Validasi URL format
- ✅ Validasi design ID extraction
- ✅ Timeout handling untuk API calls
- ✅ Retry logic untuk network errors

## 📊 Benefits

### Storage Optimization

**Sebelum:**

- Design dihapus dari database
- ImageKit assets tetap tersimpan
- Storage usage terus bertambah
- Biaya ImageKit meningkat

**Sesudah:**

- Design dihapus dari database
- ImageKit assets ikut dihapus
- Storage usage optimal
- Biaya ImageKit terkontrol

### Performance Impact

- **Minimal impact** pada delete operation
- **Async cleanup** tidak memblokir UI
- **Batch operations** untuk efisiensi
- **Error isolation** untuk reliability

## 🧪 Testing

### Test Cases

1. **Single Design Delete**

   - ✅ Delete design dengan imagePreview
   - ✅ Delete design tanpa imagePreview
   - ✅ Error handling saat ImageKit API gagal

2. **Multiple Assets per Design**

   - ✅ Design dengan multiple uploaded images
   - ✅ Design dengan AI generated images
   - ✅ Design dengan template images

3. **Edge Cases**
   - ✅ Invalid ImageKit URL
   - ✅ Network timeout
   - ✅ ImageKit API rate limiting
   - ✅ Partial cleanup failures

### Manual Testing

```javascript
// Test cleanup untuk design tertentu
const { cleanupByDesignId } = useImageKitCleanup();
await cleanupByDesignId("k17abc123");

// Test batch cleanup
const designs = [
  { imagePreview: "url1", name: "Design 1" },
  { imagePreview: "url2", name: "Design 2" },
];
await cleanupMultipleDesigns(designs);
```

## 🚀 Future Enhancements

### Planned Features

- [ ] **Scheduled Cleanup**: Cleanup orphaned assets secara berkala
- [ ] **Storage Analytics**: Dashboard untuk monitoring storage usage
- [ ] **Bulk Operations**: Mass delete dengan progress indicator
- [ ] **Asset Recovery**: Restore accidentally deleted assets
- [ ] **Cost Tracking**: Monitor ImageKit usage dan biaya

### Advanced Cleanup

- [ ] **Smart Detection**: Detect unused assets across all designs
- [ ] **Compression**: Compress old assets sebelum delete
- [ ] **Archive**: Move old assets ke cold storage
- [ ] **Audit Trail**: Log semua cleanup operations

## 📝 Maintenance

### Regular Cleanup Tasks

```bash
# Check orphaned assets (manual script)
node scripts/check-orphaned-assets.js

# Cleanup assets older than 30 days
node scripts/cleanup-old-assets.js --days=30

# Generate storage usage report
node scripts/storage-report.js
```

### Monitoring

- Monitor ImageKit storage usage
- Track cleanup success/failure rates
- Alert jika cleanup gagal berulang kali
- Dashboard untuk visualisasi storage trends

## 🔒 Security Considerations

- ImageKit credentials disimpan di environment variables
- Validation untuk prevent unauthorized deletions
- Rate limiting untuk prevent abuse
- Audit logging untuk tracking operations

## 🐛 Troubleshooting

### Common Issues

#### 1. **Files Not Being Deleted**

**Symptoms:**
- Success message muncul tapi files masih ada di ImageKit
- Console log menunjukkan "Found 0 files"

**Possible Causes:**
- Search query tidak match dengan filename
- URL parsing error
- ImageKit API permissions

**Solutions:**

```javascript
// Debug URL parsing
import { debugUrlParsing } from "@/utils/imagekitDebug";
debugUrlParsing("https://ik.imagekit.io/inuldev0/************************************8474739568.png?updatedAt=1748474743496");

// Test complete delete flow
import { testCompleteDeleteFlow } from "@/utils/imagekitDebug";
await testCompleteDeleteFlow(imageUrl);
```

#### 2. **Search Query Issues**

**Problem:** ImageKit search tidak menemukan files

**Solutions:**
```javascript
// Try different search patterns:
// 1. Exact filename
searchQuery = 'name="************************************8474739568.png"'

// 2. Design ID pattern
searchQuery = 'name:j57838na3xdc3668rmdm3esnp17grrq8*'

// 3. Partial filename
searchQuery = 'name:*1748474739568*'
```

#### 3. **URL Format Variations**

**Different URL formats:**
```
// With query parameters
https://ik.imagekit.io/inuldev0/file.png?updatedAt=123

// Without query parameters
https://ik.imagekit.io/inuldev0/file.png

// With folders
https://ik.imagekit.io/inuldev0/folder/file.png
```

### Debug Tools

#### Development Debug Panel

Add to your development page:
```jsx
import ImageKitDebugPanel from "@/components/debug/ImageKitDebugPanel";

// In development only
{process.env.NODE_ENV === 'development' && <ImageKitDebugPanel />}
```

#### Console Debugging

```javascript
// Enable detailed logging
console.log("ImageKit cleanup debugging enabled");

// Test URL parsing
const parsed = debugUrlParsing(imageUrl);

// List all files
const files = await listAllImageKitFiles(50);

// Test search queries
await searchImageKitFiles('name="exact-filename.png"');
```

### Manual Cleanup

If automatic cleanup fails, use manual methods:

```javascript
// 1. List all files and find manually
const allFiles = await listAllImageKitFiles(100);
const targetFile = allFiles.find(f => f.name.includes("target-string"));

// 2. Delete by file ID
if (targetFile) {
  await imagekit.deleteFile(targetFile.fileId);
}

// 3. Batch cleanup by pattern
const testFiles = allFiles.filter(f => f.name.startsWith("test_"));
for (const file of testFiles) {
  await imagekit.deleteFile(file.fileId);
}
```

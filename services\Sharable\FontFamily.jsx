import React from "react";
import { useCanvasHook } from "@/hooks/useCanvas";

import { FontFamilyList } from "../Options";

// Helper function untuk memastikan font ter-load
const waitForFont = async (fontFamily) => {
  if (typeof document !== "undefined" && document.fonts) {
    try {
      await document.fonts.load(`16px ${fontFamily.split(",")[0]}`);
      // Tambahan delay untuk memastikan font benar-benar siap
      await new Promise((resolve) => setTimeout(resolve, 50));
    } catch (error) {
      console.log("Font loading fallback:", fontFamily);
    }
  }
};

function FontFamily() {
  const { canvasEditor } = useCanvasHook();

  // Preload semua Google Fonts saat component mount
  React.useEffect(() => {
    const preloadFonts = async () => {
      const googleFonts = [
        "Montserrat",
        "Open Sans",
        "Roboto",
        "Lato",
        "Pop<PERSON>s",
        "Source Sans 3",
        "Playfair Display",
        "<PERSON><PERSON>weather",
        "<PERSON>",
        "Pacifico",
        "Dancing Script",
      ];

      for (const font of googleFonts) {
        try {
          await waitForFont(`${font}, sans-serif`);
        } catch (error) {
          console.log(`Failed to preload ${font}`);
        }
      }
    };

    preloadFonts();
  }, []);

  const onFontFamilyChange = async (value) => {
    const activeObject = canvasEditor?.getActiveObject();
    if (
      activeObject &&
      (activeObject.type === "i-text" || activeObject.type === "textbox")
    ) {
      try {
        // Tunggu font ter-load sepenuhnya
        await waitForFont(value);

        // Set font family
        activeObject.set({
          fontFamily: value,
        });

        // Force refresh untuk memastikan perubahan terlihat
        canvasEditor.renderAll();

        // Tambahan: Force re-render text object
        if (activeObject.type === "i-text") {
          activeObject.initDimensions();
          activeObject.setCoords();
        }

        // Debug log untuk memastikan font berubah
        console.log(
          "Font changed to:",
          value,
          "for object:",
          activeObject.type
        );
      } catch (error) {
        console.error("Error changing font:", error);
        // Fallback tanpa font loading
        activeObject.set({
          fontFamily: value,
        });
        canvasEditor.renderAll();
      }
    }
  };

  return (
    <div className="h-[200px] overflow-auto">
      {FontFamilyList.map((font, index) => (
        <h2
          key={index}
          className="text-lg p-2 bg-secondary rounded-lg mb-2 cursor-pointer hover:bg-accent transition-colors"
          style={{
            fontFamily: font.value,
          }}
          onClick={() => onFontFamilyChange(font.value)}
        >
          {font.name}
        </h2>
      ))}
    </div>
  );
}

export default FontFamily;

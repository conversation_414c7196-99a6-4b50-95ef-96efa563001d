import { toast } from "sonner";
import ImageKit from "imagekit";
import { FabricImage } from "fabric";
import { useState, useMemo } from "react";
import { useParams } from "next/navigation";
import { Loader2Icon, ImageUp } from "lucide-react";

import { useCanvasHook } from "@/hooks/useCanvas";

function UploadImage() {
  const { designId } = useParams();
  const { canvasEditor } = useCanvasHook();
  const [loading, setLoading] = useState(false);

  // Memoize ImageKit instance
  const imagekit = useMemo(
    () =>
      new ImageKit({
        publicKey: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY,
        privateKey: process.env.NEXT_PUBLIC_IMAGEKIT_PRIVATE_KEY,
        urlEndpoint: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT,
      }),
    []
  );

  const onFileUpload = async (event) => {
    let uploadToastId;

    try {
      setLoading(true);
      const file = event.target.files[0];

      if (!file) {
        toast.error("Tidak ada file yang dipilih");
        return;
      }

      // Validasi file type
      if (!file.type.startsWith("image/")) {
        toast.error("File harus berupa gambar");
        return;
      }

      // Validasi file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Ukuran file maksimal 5MB");
        return;
      }

      uploadToastId = toast.loading("Mengunggah dan menambahkan gambar...", {
        duration: Infinity,
      });

      const imageRef = await imagekit.upload({
        file: file,
        fileName: `${designId}_${Date.now()}.png`,
        isPublished: true,
        timeout: 30000,
      });

      const canvasImageRef = await FabricImage.fromURL(imageRef?.url, {
        crossOrigin: "anonymous",
        timeout: 15000,
      });

      canvasImageRef.set({
        id: `direct-upload-${Date.now()}`,
        name: "Direct Upload",
      });

      canvasEditor.add(canvasImageRef);
      canvasEditor.renderAll();

      // Dismiss loading toast sebelum show success
      toast.dismiss(uploadToastId);
      toast.success("Gambar berhasil ditambahkan ke canvas", {
        duration: 3000,
      });
    } catch (error) {
      console.error("Error uploading image:", error);

      // Dismiss loading toast jika ada error
      if (uploadToastId) {
        toast.dismiss(uploadToastId);
      }

      toast.error("Gagal mengunggah gambar", { duration: 5000 });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mb-4">
      <label
        htmlFor="uploadImage"
        className={`block p-4 bg-primary text-white rounded-lg text-center text-sm cursor-pointer hover:bg-primary/90 transition-colors ${
          loading ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        {loading ? (
          <div className="flex items-center justify-center gap-2">
            <Loader2Icon className="animate-spin w-4 h-4" />
            <span>Mengunggah...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center gap-2">
            <ImageUp className="w-4 h-4" />
            <span>Unggah Gambar Langsung</span>
          </div>
        )}
      </label>

      <input
        type="file"
        id="uploadImage"
        className="hidden"
        multiple={false}
        onChange={onFileUpload}
        accept="image/*"
        disabled={loading}
      />

      <p className="text-xs text-gray-500 text-center mt-2">
        Gambar akan langsung ditambahkan ke canvas (Max 5MB)
      </p>
    </div>
  );
}

export default UploadImage;

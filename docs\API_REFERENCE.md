# API Reference

## 📱 Mobile Hooks

### `useMobile()`

Hook untuk mendeteksi apakah device adalah mobile.

#### Usage

```javascript
import { useMobile } from "@/hooks/useMobile";

function MyComponent() {
  const isMobile = useMobile();

  return <div>{isMobile ? "Mobile View" : "Desktop View"}</div>;
}
```

#### Returns

- `boolean` - `true` jika screen width ≤ 768px

#### Example

```javascript
const isMobile = useMobile();
// true pada mobile devices (≤768px)
// false pada desktop/tablet (>768px)
```

---

### `useBreakpoint()`

Hook untuk mendeteksi berbagai breakpoint dan informasi screen.

#### Usage

```javascript
import { useBreakpoint } from "@/hooks/useMobile";

function ResponsiveComponent() {
  const { isMobile, isTablet, isDesktop, width } = useBreakpoint();

  if (isMobile) return <MobileLayout />;
  if (isTablet) return <TabletLayout />;
  return <DesktopLayout />;
}
```

#### Returns

```typescript
{
  isMobile: boolean; // ≤768px
  isTablet: boolean; // 769px - 1024px
  isDesktop: boolean; // >1024px
  width: number; // Current screen width
}
```

#### Example

```javascript
const breakpoint = useBreakpoint();

// Mobile (iPhone)
// { isMobile: true, isTablet: false, isDesktop: false, width: 375 }

// Tablet (iPad)
// { isMobile: false, isTablet: true, isDesktop: false, width: 768 }

// Desktop
// { isMobile: false, isTablet: false, isDesktop: true, width: 1920 }
```

---

## 🎨 Mobile Components

### `<MobileNavigation />`

Komponen navigasi khusus untuk mobile devices.

#### Props

```typescript
interface MobileNavigationProps {
  onSidebarToggle?: () => void;
  onLayerToggle?: () => void;
  objectCount?: number;
  showLayerButton?: boolean;
}
```

#### Usage

```javascript
import MobileNavigation from "@/services/Components/MobileNavigation";

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [layerOpen, setLayerOpen] = useState(false);

  return (
    <MobileNavigation
      onSidebarToggle={() => setSidebarOpen(true)}
      onLayerToggle={() => setLayerOpen(!layerOpen)}
      objectCount={5}
      showLayerButton={true}
    />
  );
}
```

#### Features

- **Top Header**: Hamburger menu dan layer toggle
- **Bottom Navigation**: Quick access tools dengan badge
- **Auto-hide**: Hanya tampil di mobile devices
- **Touch-friendly**: Button sizes optimized untuk touch

---

### `<SideBar />`

Enhanced sidebar dengan support untuk mobile overlay.

#### Props

```typescript
interface SideBarProps {
  isOpen?: boolean; // Mobile overlay state
  onClose?: () => void; // Mobile close handler
}
```

#### Usage

```javascript
import SideBar from "./SideBar";

function Layout() {
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMobile();

  return (
    <SideBar
      {...(isMobile && {
        isOpen: mobileOpen,
        onClose: () => setMobileOpen(false),
      })}
    />
  );
}
```

#### Behavior

- **Desktop**: Normal sidebar dengan collapse functionality
- **Mobile**: Overlay sidebar dengan slide-in animation
- **Conditional**: Automatically adapts berdasarkan screen size

---

### `<LayerPanel />`

Layer management panel dengan mobile bottom sheet support.

#### Props

```typescript
interface LayerPanelProps {
  isMinimized?: boolean;
  onMinimize?: (minimized: boolean) => void;
  isMobile?: boolean;
}
```

#### Usage

```javascript
import LayerPanel from "@/services/Components/LayerPanel";

function CanvasEditor() {
  const [minimized, setMinimized] = useState(false);
  const isMobile = useMobile();

  return (
    <LayerPanel
      isMinimized={minimized}
      onMinimize={setMinimized}
      isMobile={isMobile}
    />
  );
}
```

#### Features

- **Desktop**: Fixed positioned panel di kanan
- **Mobile**: Bottom sheet dengan swipe gestures
- **Layer Controls**: Lock, hide, reorder layers
- **Touch-optimized**: Larger touch targets untuk mobile

---

## 🎯 Canvas API

### Mobile Canvas Configuration

#### Touch-Optimized Settings

```javascript
const mobileCanvasConfig = {
  // Larger selection tolerance
  targetFindTolerance: 15,

  // Touch-friendly borders
  selectionLineWidth: 3,
  selectionDashArray: [8, 8],

  // Disable problematic features
  centeredScaling: false,
  centeredRotation: false,
};
```

#### Gesture Support

```javascript
// Pinch-to-zoom implementation
canvas.addEventListener("touchmove", (e) => {
  if (e.touches.length === 2) {
    const touch1 = e.touches[0];
    const touch2 = e.touches[1];
    const distance = calculateDistance(touch1, touch2);

    if (lastDistance > 0) {
      const scale = distance / lastDistance;
      const zoom = canvas.getZoom() * scale;

      // Apply zoom with limits
      if (zoom >= 0.5 && zoom <= 3) {
        canvas.setZoom(zoom);
        canvas.requestRenderAll();
      }
    }
    lastDistance = distance;
  }
});
```

---

## 🎨 CSS Utilities

### Mobile-Specific Classes

#### Visibility Controls

```css
.mobile-hidden {
  display: none !important;
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}
```

#### Layout Utilities

```css
.mobile-full-width {
  width: 100% !important;
}

.mobile-canvas-container {
  padding: 0.5rem !important;
  overflow-x: auto !important;
}
```

#### Overlay Components

```css
.mobile-sidebar-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 40 !important;
}

.mobile-sidebar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  height: 100vh !important;
  width: 280px !important;
  background: white !important;
  z-index: 50 !important;
  transform: translateX(-100%) !important;
  transition: transform 0.3s ease-in-out !important;
}

.mobile-sidebar.open {
  transform: translateX(0) !important;
}
```

#### Bottom Sheet

```css
.mobile-layer-panel {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  max-height: 50vh !important;
  background: white !important;
  border-top: 1px solid #e5e7eb !important;
  z-index: 45 !important;
  transform: translateY(100%) !important;
  transition: transform 0.3s ease-in-out !important;
}

.mobile-layer-panel.open {
  transform: translateY(0) !important;
}
```

---

## 🔧 Utility Functions

### Responsive Helpers

#### Conditional Props

```javascript
// Apply props only on mobile
const mobileProps = isMobile
  ? {
      variant: "mobile",
      size: "large",
      onTouch: handleTouch,
    }
  : {};

<Component {...mobileProps} />;
```

#### Conditional Rendering

```javascript
// Render different components
{isMobile ? (
  <MobileComponent />
) : (
  <DesktopComponent />
)}

// Conditional classes
className={`base-class ${isMobile ? 'mobile-class' : 'desktop-class'}`}
```

#### Touch Event Helpers

```javascript
// Calculate touch distance
function calculateDistance(touch1, touch2) {
  return Math.sqrt(
    Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
  );
}

// Prevent default touch behavior
function preventTouchDefault(element) {
  element.addEventListener(
    "touchstart",
    (e) => {
      e.preventDefault();
    },
    { passive: false }
  );

  element.addEventListener(
    "touchmove",
    (e) => {
      e.preventDefault();
    },
    { passive: false }
  );
}
```

---

## 📱 Best Practices

### Performance

- Use conditional rendering untuk avoid unnecessary components
- Implement lazy loading untuk mobile-specific components
- Debounce touch events untuk better performance

### Accessibility

- Maintain minimum 44px touch targets
- Provide clear visual feedback untuk touch interactions
- Support keyboard navigation sebagai fallback

### Testing

- Test di real devices, bukan hanya browser dev tools
- Verify touch gestures work correctly
- Check performance di low-end devices

### Code Organization

- Keep mobile logic separate dari desktop logic
- Use hooks untuk shared responsive behavior
- Document mobile-specific features clearly

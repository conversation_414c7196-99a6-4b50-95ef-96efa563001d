import { useState } from "react";

import { useCanvasHook } from "@/hooks/useCanvas";

import ColorPickerEditor from "./ColorPickerEditor";
import TransparencyButton from "./TransparencyButton";

function FillColor() {
  const [color, setColor] = useState("#000");
  const { canvasEditor } = useCanvasHook();

  const onColorChange = (color) => {
    setColor(color);
    const activeObject = canvasEditor?.getActiveObject();
    if (activeObject) {
      // Handle transparansi untuk fill color
      if (
        color === "transparent" ||
        (color.includes("rgba") && color.includes(", 0)"))
      ) {
        activeObject.set({
          fill: "transparent",
        });
      } else {
        activeObject.set({
          fill: color,
        });
      }
      canvasEditor.renderAll();
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex justify-center">
        <TransparencyButton type="fill" />
      </div>
      <ColorPickerEditor
        onColorChange={(v) => onColorChange(v)}
        value={color}
        enableAlpha={true}
      />
    </div>
  );
}

export default FillColor;

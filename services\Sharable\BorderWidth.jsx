import { Slider } from "@/components/ui/slider";
import { useCanvasHook } from "@/hooks/useCanvas";

function BorderWidth() {
  const { canvasEditor } = useCanvasHook();

  const onWidthChange = (value) => {
    const activeObject = canvasEditor.getActiveObject();
    if (activeObject) {
      activeObject.set({
        strokeWidth: value,
      });
      canvasEditor.renderAll();
    }
  };

  return (
    <div>
      <h2 className="my-2">Le<PERSON>aris</h2>
      <Slider
        defaultValue={[5]}
        max={100}
        step={1}
        onValueChange={(v) => onWidthChange(v[0])}
      />
    </div>
  );
}

export default BorderWidth;

"use client";

import { Suspense } from "react";
import { ConvexProvider, ConvexReactClient } from "convex/react";

import Provider from "./provider";

function ConvexClientProvider({ children }) {
  const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL);

  return (
    <ConvexProvider client={convex}>
      <Suspense
        fallback={
          <div className="fixed inset-0 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 flex items-center justify-center z-50">
            <div className="bg-white shadow-xl border p-8 rounded-xl flex flex-col items-center gap-4">
              <div className="animate-spin w-8 h-8 border-3 border-purple-600 border-t-transparent rounded-full"></div>
              <span className="text-gray-700 font-medium text-lg">
                Memulai CanvaIn...
              </span>
              <p className="text-gray-500 text-sm">Menyiapkan workspace Anda</p>
            </div>
          </div>
        }
      >
        <Provider>{children}</Provider>
      </Suspense>
    </ConvexProvider>
  );
}

export default ConvexClientProvider;

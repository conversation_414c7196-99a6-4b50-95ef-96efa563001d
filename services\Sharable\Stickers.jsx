import Image from "next/image";
import { FabricImage } from "fabric";

import { useCanvasHook } from "@/hooks/useCanvas";

import { StickerList } from "../Options";

function Stickers() {
  const { canvasEditor } = useCanvasHook();

  const onAddSticker = async (imageUrl) => {
    if (canvasEditor) {
      const canvasImageRef = await FabricImage.fromURL(imageUrl, {
        crossOrigin: "anonymous",
        timeout: 15000,
      });

      canvasEditor.add(canvasImageRef);
    }
  };

  return (
    <div className="mt-5">
      <h2 className="text-md font-bold">Stiker</h2>
      <div className="grid grid-cols-3 gap-7 h-[50vh] overflow-auto">
        {StickerList.map((sticker, index) => (
          <Image
            src={sticker}
            alt={sticker}
            onClick={() => onAddSticker(sticker)}
            key={index}
            width={100}
            height={100}
            className="w-full h-[50px] object-contain cursor-pointer"
          />
        ))}
      </div>
    </div>
  );
}

export default Stickers;

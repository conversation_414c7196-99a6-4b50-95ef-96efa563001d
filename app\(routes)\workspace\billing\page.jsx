"use client";

import Image from "next/image";
import { useState, useContext } from "react";
import { useMutation, useQuery } from "convex/react";
import {
  Crown,
  Star,
  Check,
  Clock,
  Mail,
  MessageCircle,
  Sparkles,
  Zap,
  Shield,
} from "lucide-react";

import { api } from "@/convex/_generated/api";
import { UserDetailContext } from "@/context/UserDetailContext";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

import { getSubscriptionDisplayInfo } from "@/lib/subscription";

function BillingPage() {
  const [email, setEmail] = useState("");
  const [feedback, setFeedback] = useState("");
  const { userDetail } = useContext(UserDetailContext);
  const updateUserSubscription = useMutation(api.users.UpdateUserSubscription);

  // Get user subscription info
  const subscriptionInfo = useQuery(
    api.users.GetUserSubscriptionInfo,
    userDetail?._id ? { userId: userDetail._id } : "skip"
  );

  const currentSubscriptionType = subscriptionInfo?.subscriptionType || "free";
  const isPremium = subscriptionInfo?.isPremium || false;

  const handleNotifyMe = () => {
    if (!email) {
      toast.error("Masukkan email Anda");
      return;
    }
    toast.success(
      "Terima kasih! Kami akan memberitahu Anda saat fitur premium tersedia."
    );
    setEmail("");
  };

  const handleFeedback = () => {
    if (!feedback) {
      toast.error("Masukkan feedback Anda");
      return;
    }
    toast.success(
      "Terima kasih atas feedback Anda! Ini sangat membantu pengembangan kami."
    );
    setFeedback("");
  };

  // Simulasi upgrade untuk testing (akan diganti dengan payment gateway)
  const handleUpgrade = async (subscriptionType) => {
    if (!userDetail?._id) return;

    try {
      toast.loading("Mengupgrade akun...", { id: "upgrade-subscription" });
      await updateUserSubscription({
        userId: userDetail._id,
        subscriptionType: subscriptionType,
        subscriptionStatus: "active",
        subscriptionExpiresAt: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 hari
      });
      toast.success(`Berhasil upgrade ke ${subscriptionType.toUpperCase()}!`, {
        id: "upgrade-subscription",
        duration: 3000,
      });
    } catch (error) {
      toast.error("Gagal upgrade akun", { id: "upgrade-subscription" });
    }
  };

  const plannedFeatures = [
    {
      icon: <Sparkles className="h-5 w-5" />,
      title: "AI Design Assistant",
      description:
        "AI yang membantu membuat desain otomatis berdasarkan brief Anda",
    },
    {
      icon: <Zap className="h-5 w-5" />,
      title: "Premium Templates",
      description: "Akses ke ribuan template premium dari desainer profesional",
    },
    {
      icon: <Crown className="h-5 w-5" />,
      title: "Advanced Export",
      description:
        "Export dalam format premium: SVG, PDF vector, dan resolusi tinggi",
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: "Team Collaboration",
      description: "Kolaborasi real-time dengan tim dan sharing workspace",
    },
  ];

  return (
    <div className="p-10 w-full max-w-6xl mx-auto">
      {/* Header */}
      <div className="relative mb-10">
        <Image
          src={"/banner.png"}
          alt="banner"
          width={1800}
          height={300}
          className="w-full h-[200px] rounded-2xl object-cover"
        />
        <div className="absolute bottom-5 left-10 text-white">
          <div className="flex items-center gap-3 mb-2">
            <h2 className="text-3xl font-bold">Pembayaran & Premium</h2>
            <Badge variant="secondary" className="bg-yellow-500 text-white">
              <Clock className="h-3 w-3 mr-1" />
              Coming Soon
            </Badge>
          </div>
          <p className="text-lg opacity-90">
            Fitur premium sedang dalam pengembangan
          </p>
        </div>
      </div>

      {/* Current Status */}
      <Card
        className={`mb-8 ${isPremium ? "border-purple-200 bg-purple-50" : "border-green-200 bg-green-50"}`}
      >
        <CardHeader>
          <CardTitle
            className={`flex items-center gap-2 ${isPremium ? "text-purple-700" : "text-green-700"}`}
          >
            {isPremium ? (
              <Crown className="h-5 w-5" />
            ) : (
              <Check className="h-5 w-5" />
            )}
            Status Saat Ini:{" "}
            {getSubscriptionDisplayInfo(currentSubscriptionType).name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p
            className={`${isPremium ? "text-purple-600" : "text-green-600"} mb-4`}
          >
            {isPremium
              ? "Anda adalah pengguna premium dengan akses penuh ke semua fitur!"
              : "Saat ini Anda menggunakan akun gratis dengan batasan tertentu."}
          </p>
          <div className="grid md:grid-cols-3 gap-4">
            {getSubscriptionDisplayInfo(currentSubscriptionType).features.map(
              (feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Check
                    className={`h-4 w-4 ${isPremium ? "text-purple-500" : "text-green-500"}`}
                  />
                  <span className="text-sm">{feature}</span>
                </div>
              )
            )}
          </div>
        </CardContent>
      </Card>

      {/* Pricing Plans - hanya tampil untuk free users */}
      {!isPremium && (
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-6 text-center">
            Pilih Paket Berlangganan
          </h3>
          <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {/* Pro Plan */}
            <Card className="border-purple-200 relative">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-purple-700">Pro</CardTitle>
                  <Badge className="bg-purple-600">Populer</Badge>
                </div>
                <div className="text-3xl font-bold text-purple-700">
                  Rp 49.000<span className="text-sm font-normal">/bulan</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Unlimited Designs</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">
                      All Export Formats (PNG, SVG, PDF)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Premium Templates</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Priority Support</span>
                  </div>
                </div>
                <Button
                  onClick={() => handleUpgrade("pro")}
                  className="w-full bg-purple-600 hover:bg-purple-700"
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade ke Pro
                </Button>
                <p className="text-xs text-gray-500 text-center">
                  *Simulasi untuk testing - akan diganti dengan payment gateway
                </p>
              </CardContent>
            </Card>

            {/* Team Plan */}
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="text-blue-700">Team</CardTitle>
                <div className="text-3xl font-bold text-blue-700">
                  Rp 99.000<span className="text-sm font-normal">/bulan</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Semua fitur Pro</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Team Collaboration</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Shared Workspace</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Advanced Analytics</span>
                  </div>
                </div>
                <Button
                  onClick={() => handleUpgrade("team")}
                  variant="outline"
                  className="w-full border-blue-600 text-blue-600 hover:bg-blue-50"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Upgrade ke Team
                </Button>
                <p className="text-xs text-gray-500 text-center">
                  *Simulasi untuk testing - akan diganti dengan payment gateway
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Planned Premium Features */}
      <div className="mb-8">
        <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <Star className="h-6 w-6 text-yellow-500" />
          Fitur Premium yang Akan Datang
        </h3>
        <div className="grid md:grid-cols-2 gap-6">
          {plannedFeatures.map((feature, index) => (
            <Card
              key={index}
              className="border-purple-200 hover:shadow-lg transition-shadow"
            >
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-purple-700">
                  {feature.icon}
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Timeline */}
      <Card className="mb-8 border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-700">
            <Clock className="h-5 w-5" />
            Roadmap Pengembangan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <p className="font-medium">Q1 2025 - Core Features</p>
                <p className="text-sm text-gray-600">
                  Layer management, advanced tools ✅
                </p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div>
                <p className="font-medium">Q2 2025 - Premium Features</p>
                <p className="text-sm text-gray-600">
                  AI assistant, premium templates
                </p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <div>
                <p className="font-medium">Q3 2025 - Team Features</p>
                <p className="text-sm text-gray-600">
                  Collaboration, team workspace
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notify Me Section */}
      <div className="grid md:grid-cols-2 gap-8">
        <Card className="border-purple-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Mail className="h-5 w-5" />
              Beritahu Saya
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              Dapatkan notifikasi pertama saat fitur premium tersedia dengan
              harga early bird!
            </p>
            <div className="space-y-3">
              <Input
                placeholder="Email Anda"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                type="email"
              />
              <Button onClick={handleNotifyMe} className="w-full">
                <Mail className="h-4 w-4 mr-2" />
                Notify Me
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-700">
              <MessageCircle className="h-5 w-5" />
              Feedback & Saran
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              Bantu kami mengembangkan fitur yang Anda butuhkan!
            </p>
            <div className="space-y-3">
              <Textarea
                placeholder="Fitur apa yang Anda inginkan?"
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                rows={3}
              />
              <Button
                onClick={handleFeedback}
                variant="outline"
                className="w-full"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Kirim Feedback
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* FAQ */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Pertanyaan Umum</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">
              Apakah akan tetap ada versi gratis?
            </h4>
            <p className="text-gray-600 text-sm">
              Ya! Fitur dasar CanvaIn akan tetap gratis selamanya. Premium
              features adalah tambahan untuk kebutuhan profesional.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">
              Kapan fitur premium akan tersedia?
            </h4>
            <p className="text-gray-600 text-sm">
              Kami menargetkan Q2 2025 untuk peluncuran fitur premium pertama.
              Daftar di "Notify Me" untuk update terbaru.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">
              Berapa harga yang direncanakan?
            </h4>
            <p className="text-gray-600 text-sm">
              Kami masih menentukan pricing yang fair. Early subscribers akan
              mendapat harga khusus!
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default BillingPage;

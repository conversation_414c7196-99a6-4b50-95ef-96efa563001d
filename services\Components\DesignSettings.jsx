import { toast } from "sonner";
import React, { useState, useEffect } from "react";
import {
  Monitor,
  Moon,
  Sun,
  Grid3X3,
  Save,
  Download,
  Palette,
  Settings2,
  <PERSON>otateCcw,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useCanvasHook } from "@/hooks/useCanvas";

function DesignSettings() {
  const { canvasEditor } = useCanvasHook();

  // Settings state
  const [settings, setSettings] = useState({
    theme: "light",
    language: "id",
    showGrid: false,
    snapToGrid: false,
    snapToObjects: true,
    autoSave: true,
    autoSaveInterval: 30,
    defaultExportFormat: "png",
    exportQuality: 80,
    gridSize: 20,
    zoomStep: 10,
  });

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem("canvain-settings");
    if (savedSettings) {
      setSettings((prev) => ({ ...prev, ...JSON.parse(savedSettings) }));
    }
  }, []);

  // Save settings to localStorage
  const saveSettings = (newSettings) => {
    setSettings(newSettings);
    localStorage.setItem("canvain-settings", JSON.stringify(newSettings));
    toast.success("Pengaturan disimpan!");
  };

  const updateSetting = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);

    // Apply canvas-specific settings immediately
    if (canvasEditor) {
      switch (key) {
        case "showGrid":
          // Toggle grid visibility (would need custom implementation)
          console.log("Grid visibility:", value);
          break;
        case "snapToGrid":
          canvasEditor.snapToGrid = value;
          break;
        case "snapToObjects":
          canvasEditor.snapToObject = value;
          break;
      }
    }
  };

  const resetSettings = () => {
    const defaultSettings = {
      theme: "light",
      language: "id",
      showGrid: false,
      snapToGrid: false,
      snapToObjects: true,
      autoSave: true,
      autoSaveInterval: 30,
      defaultExportFormat: "png",
      exportQuality: 80,
      gridSize: 20,
      zoomStep: 10,
    };
    saveSettings(defaultSettings);
    toast.success("Pengaturan direset ke default!");
  };

  return (
    <div className="space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto">
      {/* Canvas Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-sm">
            <Grid3X3 className="h-4 w-4" />
            Canvas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="text-sm">Show Grid</label>
            <Switch
              checked={settings.showGrid}
              onCheckedChange={(checked) => updateSetting("showGrid", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm">Snap to Grid</label>
            <Switch
              checked={settings.snapToGrid}
              onCheckedChange={(checked) =>
                updateSetting("snapToGrid", checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm">Snap to Objects</label>
            <Switch
              checked={settings.snapToObjects}
              onCheckedChange={(checked) =>
                updateSetting("snapToObjects", checked)
              }
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm">Grid Size: {settings.gridSize}px</label>
            <Slider
              value={[settings.gridSize]}
              onValueChange={([value]) => updateSetting("gridSize", value)}
              max={50}
              min={10}
              step={5}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Appearance Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-sm">
            <Palette className="h-4 w-4" />
            Appearance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm">Theme</label>
            <Select
              value={settings.theme}
              onValueChange={(value) => updateSetting("theme", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">
                  <div className="flex items-center gap-2">
                    <Sun className="h-4 w-4" />
                    Light
                  </div>
                </SelectItem>
                <SelectItem value="dark">
                  <div className="flex items-center gap-2">
                    <Moon className="h-4 w-4" />
                    Dark
                  </div>
                </SelectItem>
                <SelectItem value="system">
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    System
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm">Language</label>
            <Select
              value={settings.language}
              onValueChange={(value) => updateSetting("language", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="id">🇮🇩 Bahasa Indonesia</SelectItem>
                <SelectItem value="en">🇺🇸 English</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Auto Save Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-sm">
            <Save className="h-4 w-4" />
            Auto Save
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="text-sm">Enable Auto Save</label>
            <Switch
              checked={settings.autoSave}
              onCheckedChange={(checked) => updateSetting("autoSave", checked)}
            />
          </div>

          {settings.autoSave && (
            <div className="space-y-2">
              <label className="text-sm">
                Interval: {settings.autoSaveInterval}s
              </label>
              <Slider
                value={[settings.autoSaveInterval]}
                onValueChange={([value]) =>
                  updateSetting("autoSaveInterval", value)
                }
                max={300}
                min={10}
                step={10}
                className="w-full"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Export Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-sm">
            <Download className="h-4 w-4" />
            Export
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm">Default Format</label>
            <Select
              value={settings.defaultExportFormat}
              onValueChange={(value) =>
                updateSetting("defaultExportFormat", value)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="png">PNG</SelectItem>
                <SelectItem value="jpg">JPG</SelectItem>
                <SelectItem value="webp">WebP</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm">
              Quality: {settings.exportQuality}%
            </label>
            <Slider
              value={[settings.exportQuality]}
              onValueChange={([value]) => updateSetting("exportQuality", value)}
              max={100}
              min={10}
              step={10}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Reset Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-sm">
            <Settings2 className="h-4 w-4" />
            Reset
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button variant="outline" onClick={resetSettings} className="w-full">
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Default
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

export default DesignSettings;

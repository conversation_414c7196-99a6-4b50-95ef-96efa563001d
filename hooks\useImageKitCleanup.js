/**
 * Custom hook untuk ImageKit cleanup operations
 * Mengelola cleanup assets saat design dihapus
 */

import { useCallback } from "react";
import { toast } from "sonner";
import { deleteImageKitFile, cleanupDesignAssets } from "@/utils/imagekitUtils";

export const useImageKitCleanup = () => {
  /**
   * Cleanup single design assets
   * @param {string} imagePreviewUrl - URL preview image dari design
   * @param {string} designName - Nama design untuk logging
   * @returns {Promise<boolean>} Success status
   */
  const cleanupSingleDesign = useCallback(
    async (imagePreviewUrl, designName = "Unknown") => {
      if (!imagePreviewUrl) {
        console.warn("No image preview URL provided for cleanup");
        return false;
      }

      try {
        console.log(`Starting cleanup for design: ${designName}`);
        const success = await deleteImageKitFile(imagePreviewUrl);

        if (success) {
          console.log(`Successfully cleaned up assets for: ${designName}`);
          return true;
        } else {
          console.warn(`Cleanup completed with warnings for: ${designName}`);
          return false;
        }
      } catch (error) {
        console.error(`Error cleaning up assets for ${designName}:`, error);
        return false;
      }
    },
    []
  );

  /**
   * Cleanup multiple designs assets
   * @param {Array} designs - Array of design objects dengan imagePreview
   * @returns {Promise<{success: number, failed: number}>} Cleanup results
   */
  const cleanupMultipleDesigns = useCallback(
    async (designs) => {
      if (!designs || !Array.isArray(designs)) {
        return { success: 0, failed: 0 };
      }

      let successCount = 0;
      let failedCount = 0;

      console.log(`Starting batch cleanup for ${designs.length} designs`);

      for (const design of designs) {
        try {
          if (design.imagePreview) {
            const success = await cleanupSingleDesign(
              design.imagePreview,
              design.name
            );
            if (success) {
              successCount++;
            } else {
              failedCount++;
            }
          } else {
            console.warn(`No image preview for design: ${design.name}`);
            failedCount++;
          }
        } catch (error) {
          console.error(`Error in batch cleanup for ${design.name}:`, error);
          failedCount++;
        }
      }

      console.log(
        `Batch cleanup completed: ${successCount} success, ${failedCount} failed`
      );
      return { success: successCount, failed: failedCount };
    },
    [cleanupSingleDesign]
  );

  /**
   * Cleanup dengan toast notifications
   * @param {string} imagePreviewUrl - URL preview image
   * @param {string} designName - Nama design
   * @returns {Promise<boolean>} Success status
   */
  const cleanupWithToast = useCallback(
    async (imagePreviewUrl, designName = "Unknown") => {
      if (!imagePreviewUrl) {
        return false;
      }

      let cleanupToastId;

      try {
        cleanupToastId = toast.loading("Membersihkan assets...", {
          duration: Infinity,
        });

        const success = await cleanupSingleDesign(imagePreviewUrl, designName);

        toast.dismiss(cleanupToastId);

        if (success) {
          toast.success("Assets berhasil dibersihkan", { duration: 2000 });
          return true;
        } else {
          toast.warning("Cleanup selesai dengan peringatan", {
            duration: 2000,
          });
          return false;
        }
      } catch (error) {
        if (cleanupToastId) {
          toast.dismiss(cleanupToastId);
        }

        toast.error("Gagal membersihkan assets", { duration: 3000 });
        return false;
      }
    },
    [cleanupSingleDesign]
  );

  /**
   * Cleanup berdasarkan design ID (untuk cleanup manual)
   * @param {string} designId - ID design untuk cleanup
   * @returns {Promise<number>} Jumlah files yang dihapus
   */
  const cleanupByDesignId = useCallback(async (designId) => {
    if (!designId) {
      console.warn("No design ID provided for cleanup");
      return 0;
    }

    try {
      console.log(`Manual cleanup for design ID: ${designId}`);
      const deletedCount = await cleanupDesignAssets(designId);

      if (deletedCount > 0) {
        console.log(`Manual cleanup successful: ${deletedCount} files removed`);
        toast.success(`${deletedCount} assets berhasil dibersihkan`, {
          duration: 2000,
        });
      } else {
        console.log("No assets found for cleanup");
        toast.info("Tidak ada assets yang perlu dibersihkan", {
          duration: 2000,
        });
      }

      return deletedCount;
    } catch (error) {
      console.error("Error in manual cleanup:", error);
      toast.error("Gagal melakukan cleanup manual", { duration: 3000 });
      return 0;
    }
  }, []);

  return {
    cleanupSingleDesign,
    cleanupMultipleDesigns,
    cleanupWithToast,
    cleanupByDesignId,
  };
};

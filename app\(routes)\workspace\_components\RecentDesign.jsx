"use client";

import Image from "next/image";
import { useQuery } from "convex/react";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";

import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { UserDetailContext } from "@/context/UserDetailContext";

import DesignActionsMenu from "./DesignActionsMenu";
import CustomCanvasDialog from "./CustomCanvasDialog";

function RecentDesign() {
  const router = useRouter();
  const { userDetail } = useContext(UserDetailContext);
  const [refreshKey, setRefreshKey] = useState(0);

  // Gunakan useQuery untuk real-time updates
  const designList = useQuery(
    api.designs.GetUserDesigns,
    userDetail?._id ? { uid: userDetail._id } : "skip"
  );

  // Refresh data ketika user kembali ke halaman
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Trigger refresh dengan mengubah key
        setRefreshKey((prev) => prev + 1);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, []);

  // Auto refresh setiap 30 detik untuk memastikan data terbaru
  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshKey((prev) => prev + 1);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Trigger refresh ketika designList berubah
  useEffect(() => {
    if (designList) {
      console.log(
        "RecentDesign: designList updated, triggering refresh",
        designList.length
      );
      setRefreshKey((prev) => prev + 1);
    }
  }, [designList]);

  // Debug logging
  useEffect(() => {
    console.log("RecentDesign: Current refreshKey:", refreshKey);
    console.log(
      "RecentDesign: Current designList:",
      designList?.length || 0,
      "designs"
    );
  }, [refreshKey, designList]);

  /**
   * Handle design deleted callback
   */
  const handleDesignDeleted = (_deletedDesignId) => {
    // Trigger refresh to update the list
    setRefreshKey((prev) => prev + 1);
  };

  /**
   * Handle design duplicated callback
   */
  const handleDesignDuplicated = (_newDesignId) => {
    // Trigger refresh to update the list
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <div className="mt-7">
      <h2 className="font-bold text-2xl">Desain Terakhir</h2>

      {designList?.length == 0 ? (
        <div className="flex flex-col gap-4 items-center mt-5">
          <Image src={"/edittool.png"} alt="edit" width={100} height={100} />
          <h2 className="text-center">
            Tidak ada desain yang dibuat, silahkan buat desain baru!
          </h2>
          <CustomCanvasDialog>
            <Button>+ Buat Desain Baru</Button>
          </CustomCanvasDialog>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5 mt-5">
          {designList?.map((design, index) => (
            <div key={index} className="bg-secondary rounded-lg relative group">
              <div
                className="cursor-pointer"
                onClick={() => router.push("/design/" + design?._id)}
              >
                {design?.imagePreview ? (
                  <Image
                    src={`${design?.imagePreview}${design?.imagePreview.includes("?") ? "&" : "?"}v=${refreshKey}&cache=${Date.now()}`}
                    alt={design?.name}
                    width={300}
                    height={300}
                    className="w-full h-[200px] object-contain rounded-t-lg"
                    key={`${design?._id}-${refreshKey}-${Date.now()}`}
                    unoptimized={true}
                    priority={false}
                    onError={(e) => {
                      console.log(
                        "Image load error for design:",
                        design?._id,
                        e
                      );
                    }}
                    onLoad={() => {
                      console.log(
                        "Image loaded successfully for design:",
                        design?._id
                      );
                    }}
                  />
                ) : (
                  <div className="w-full h-[200px] bg-gray-200 rounded-t-lg flex items-center justify-center">
                    <span className="text-gray-500 text-sm">No Preview</span>
                  </div>
                )}
              </div>

              {/* Design Info and Actions */}
              <div className="p-3 flex justify-between items-center">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {design?.name}
                  </h3>
                  <p className="text-xs text-gray-500">
                    {design?.createdAt
                      ? new Date(design.createdAt).toLocaleDateString("id-ID")
                      : ""}
                  </p>
                </div>

                {/* Actions Menu */}
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <DesignActionsMenu
                    design={design}
                    onDesignDeleted={handleDesignDeleted}
                    onDesignDuplicated={handleDesignDuplicated}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default RecentDesign;

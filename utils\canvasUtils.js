/**
 * Utility functions untuk canvas operations dan duplikasi object
 */

/**
 * Clone selected object dengan pendekatan yang lebih sederhana dan stabil
 * @param {fabric.Canvas} canvas - Fabric.js canvas instance
 * @returns {Promise<fabric.Object|null>} - Cloned object atau null jika gagal
 */
export const cloneSelectedObject = async (canvas) => {
  if (!canvas) {
    console.warn("Canvas tidak tersedia");
    return null;
  }

  const activeObject = canvas.getActiveObject();
  if (!activeObject) {
    console.warn("Tidak ada object yang dipilih");
    return null;
  }

  try {
    // Gunakan JSON serialization untuk clone yang lebih stabil
    const objectJSON = JSON.stringify(activeObject.toObject());
    const objectData = JSON.parse(objectJSON);

    // Import fabric classes yang diperlukan
    const { Rect, Circle, Triangle, Line, IText, Textbox, FabricImage } =
      await import("fabric");

    let clonedObj = null;

    // Buat object baru berdasarkan type dengan error handling per type
    switch (activeObject.type) {
      case "rect":
        clonedObj = new Rect(objectData);
        break;

      case "circle":
        clonedObj = new Circle(objectData);
        break;

      case "triangle":
        clonedObj = new Triangle(objectData);
        break;

      case "line":
        // Line memerlukan koordinat khusus
        const coords = [
          objectData.x1 || 0,
          objectData.y1 || 0,
          objectData.x2 || 100,
          objectData.y2 || 100,
        ];
        clonedObj = new Line(coords, objectData);
        break;

      case "i-text":
        clonedObj = new IText(activeObject.text || "Text", objectData);
        break;

      case "textbox":
        clonedObj = new Textbox(activeObject.text || "Text", objectData);
        break;

      case "image":
        // Untuk image, coba clone dengan cara yang lebih aman
        if (activeObject._element && activeObject._element.src) {
          try {
            clonedObj = await new Promise((resolve, reject) => {
              FabricImage.fromURL(
                activeObject._element.src,
                (img) => {
                  if (img) {
                    // Copy properties dari object asli
                    img.set(objectData);
                    resolve(img);
                  } else {
                    reject(new Error("Gagal load image"));
                  }
                },
                { crossOrigin: "anonymous" }
              );
            });
          } catch (imgError) {
            console.warn("Image clone gagal:", imgError);
            return null;
          }
        } else {
          console.warn("Image source tidak tersedia untuk clone");
          return null;
        }
        break;

      default:
        console.warn(
          `Object type '${activeObject.type}' tidak didukung untuk clone`
        );
        return null;
    }

    if (!clonedObj) {
      console.warn("Gagal membuat cloned object");
      return null;
    }

    // Set posisi dan properties untuk object baru
    clonedObj.set({
      left: (activeObject.left || 0) + 10,
      top: (activeObject.top || 0) + 10,
      id: generateObjectId(activeObject.type || "object"),
      name: activeObject.name ? `${activeObject.name} - Copy` : "Cloned Object",
    });

    // Tambahkan ke canvas
    canvas.add(clonedObj);
    canvas.setActiveObject(clonedObj);
    canvas.renderAll();

    console.log(`Successfully cloned ${activeObject.type} object`);
    return clonedObj;
  } catch (error) {
    console.error("Error while cloning object:", error);
    return null;
  }
};

/**
 * Clone multiple selected objects
 * @param {fabric.Canvas} canvas - Fabric.js canvas instance
 * @returns {Promise<fabric.Object[]>} - Array of cloned objects
 */
export const cloneSelectedObjects = async (canvas) => {
  if (!canvas) {
    console.warn("Canvas tidak tersedia");
    return [];
  }

  const activeSelection = canvas.getActiveObject();
  if (!activeSelection) {
    console.warn("Tidak ada object yang dipilih");
    return [];
  }

  const clonedObjects = [];

  try {
    // Jika multiple selection (group)
    if (activeSelection.type === "activeSelection") {
      const objects = activeSelection.getObjects();

      for (const obj of objects) {
        // Set object sebagai active untuk clone
        canvas.setActiveObject(obj);
        const cloned = await cloneSelectedObject(canvas);

        if (cloned) {
          clonedObjects.push(cloned);
        }
      }

      // Clear selection setelah clone
      canvas.discardActiveObject();
    } else {
      // Single object
      const cloned = await cloneSelectedObject(canvas);
      if (cloned) {
        clonedObjects.push(cloned);
      }
    }

    canvas.renderAll();
    return clonedObjects;
  } catch (error) {
    console.error("Error while cloning objects:", error);
    return [];
  }
};

/**
 * Generate unique ID untuk object
 * @param {string} type - Type of object
 * @returns {string} - Unique ID
 */
export const generateObjectId = (type = "object") => {
  return `${type}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Get object info untuk debugging
 * @param {fabric.Object} obj - Fabric object
 * @returns {object} - Object info
 */
export const getObjectInfo = (obj) => {
  if (!obj) return null;

  return {
    id: obj.id,
    name: obj.name,
    type: obj.type,
    left: obj.left,
    top: obj.top,
    width: obj.width,
    height: obj.height,
    scaleX: obj.scaleX,
    scaleY: obj.scaleY,
    angle: obj.angle,
  };
};

import { useCanvasHook } from "@/hooks/useCanvas";
import ObjectToolbar from "../Components/ObjectToolbar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { shapesSettingsList } from "../Options";

function ShapesSettings() {
  const { canvasEditor } = useCanvasHook();

  return (
    <div className="flex gap-6 items-center">
      {shapesSettingsList.map((shape, index) => (
        <div
          key={index}
          className="hover:scale-105 transition-all cursor-pointer"
        >
          <Popover>
            <PopoverTrigger asChild>
              <shape.icon />
            </PopoverTrigger>
            <PopoverContent>{shape.component}</PopoverContent>
          </Popover>
        </div>
      ))}

      {/* Object Operations Toolbar */}
      <ObjectToolbar />
    </div>
  );
}

export default ShapesSettings;

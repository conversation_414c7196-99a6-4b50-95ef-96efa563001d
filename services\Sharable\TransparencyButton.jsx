import { useState, useEffect } from "react";

import { Button } from "@/components/ui/button";
import { useCanvasHook } from "@/hooks/useCanvas";

function TransparencyButton({ type = "fill" }) {
  const { canvasEditor } = useCanvasHook();
  const [isTransparent, setIsTransparent] = useState(false);

  useEffect(() => {
    if (!canvasEditor) return;

    const handleSelectionChange = () => {
      const activeObject = canvasEditor.getActiveObject();
      if (activeObject) {
        let currentValue;
        if (type === "fill") {
          currentValue = activeObject.fill;
        } else if (type === "stroke") {
          currentValue = activeObject.stroke;
        } else if (type === "background") {
          currentValue = canvasEditor.backgroundColor;
        }

        setIsTransparent(currentValue === "transparent" || currentValue === "");
      }
    };

    canvasEditor.on("selection:created", handleSelectionChange);
    canvasEditor.on("selection:updated", handleSelectionChange);
    canvasEditor.on("selection:cleared", () => setIsTransparent(false));

    return () => {
      canvasEditor.off("selection:created", handleSelectionChange);
      canvasEditor.off("selection:updated", handleSelectionChange);
      canvasEditor.off("selection:cleared", () => setIsTransparent(false));
    };
  }, [canvasEditor, type]);

  const toggleTransparency = () => {
    if (type === "background") {
      if (isTransparent) {
        canvasEditor?.set({
          backgroundColor: "#ffffff",
        });
      } else {
        canvasEditor?.set({
          backgroundColor: "transparent",
        });
      }
      canvasEditor?.renderAll();
      setIsTransparent(!isTransparent);
    } else {
      const activeObject = canvasEditor?.getActiveObject();
      if (activeObject) {
        if (type === "fill") {
          activeObject.set({
            fill: isTransparent ? "#000000" : "transparent",
          });
        } else if (type === "stroke") {
          activeObject.set({
            stroke: isTransparent ? "#000000" : "transparent",
          });
        }
        canvasEditor?.renderAll();
        setIsTransparent(!isTransparent);
      }
    }
  };

  const getButtonText = () => {
    if (type === "background") {
      return isTransparent ? "Background Solid" : "Background Transparan";
    } else if (type === "fill") {
      return isTransparent ? "Fill Solid" : "Fill Transparan";
    } else if (type === "stroke") {
      return isTransparent ? "Border Solid" : "Border Transparan";
    }
  };

  return (
    <Button
      variant={isTransparent ? "default" : "outline"}
      size="sm"
      onClick={toggleTransparency}
      className="text-xs"
    >
      {getButtonText()}
    </Button>
  );
}

export default TransparencyButton;

"use client";

import Image from "next/image";
import { useContext } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useQuery } from "convex/react";
import { toast } from "sonner";

import { api } from "@/convex/_generated/api";
import { UserDetailContext } from "@/context/UserDetailContext";

function PreTemplatesList() {
  const router = useRouter();
  const { userDetail } = useContext(UserDetailContext);
  const templateList = useQuery(api.templates.GetAllTemplatest);

  const createNewDesignFromTemplate = useMutation(
    api.designs.CreateDesignFromTemplate
  );

  const onTemplateSelect = async (template) => {
    // Validasi user sudah login dan data ter-load
    if (!userDetail?._id) {
      toast.error("Mohon tunggu, sedang memuat data user...");
      return;
    }

    try {
      const id = await createNewDesignFromTemplate({
        imagePreview: template?.imagePreview,
        jsonTemplate: template?.jsonData,
        name: template?.name,
        uid: userDetail._id,
        width: template?.width,
        height: template?.height,
      });

      router.push("/design/" + id);
    } catch (error) {
      toast.error(error.message || "Gagal membuat desain dari template");
    }
  };

  return (
    <div>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5 mt-5">
        {templateList?.map((design, index) => (
          <div
            key={index}
            className="bg-secondary rounded-lg"
            onClick={() => onTemplateSelect(design)}
          >
            <Image
              src={design?.imagePreview}
              alt={design?.name}
              width={300}
              height={300}
              className="w-full cursor-pointer h-[200px] object-contain rounded-lg"
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export default PreTemplatesList;

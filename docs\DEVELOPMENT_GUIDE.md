# Development Guide

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm atau yarn
- Git

### Setup Development Environment

```bash
# Clone repository
git clone https://github.com/your-username/canvain.git
cd canvain

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env.local
# Edit .env.local dengan konfigurasi Anda

# Start development server
npm run dev

# Start Convex backend (terminal terpisah)
npx convex dev
```

## 📱 Mobile Development Guidelines

### Responsive Design Principles

#### 1. Mobile-First Approach

```css
/* Base styles untuk mobile */
.component {
  padding: 0.5rem;
  font-size: 0.875rem;
}

/* Desktop enhancements */
@media (min-width: 768px) {
  .component {
    padding: 1rem;
    font-size: 1rem;
  }
}
```

#### 2. Breakpoint Strategy

```javascript
// Breakpoints yang digunakan
const breakpoints = {
  mobile: "≤768px",
  tablet: "769px - 1024px",
  desktop: ">1024px",
};

// Usage dengan Tailwind
className = "text-sm md:text-base lg:text-lg";
```

#### 3. Touch-Friendly Design

```javascript
// Minimum touch target size
const TOUCH_TARGET_SIZE = 44; // pixels

// Touch-friendly button
<Button className="min-h-[44px] min-w-[44px] p-3" size="lg">
  Touch Me
</Button>;
```

### Mobile Components Architecture

#### 1. Conditional Rendering Pattern

```javascript
import { useMobile } from "@/hooks/useMobile";

function ResponsiveComponent() {
  const isMobile = useMobile();

  return <>{isMobile ? <MobileLayout /> : <DesktopLayout />}</>;
}
```

#### 2. Mobile Hook Usage

```javascript
// Basic mobile detection
const isMobile = useMobile();

// Advanced breakpoint detection
const { isMobile, isTablet, isDesktop, width } = useBreakpoint();

// Conditional props
<Component
  {...(isMobile && {
    variant: "mobile",
    onTouch: handleTouch,
  })}
/>;
```

#### 3. Mobile State Management

```javascript
function MobileAwareComponent() {
  // Mobile-specific states
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [touchStartPos, setTouchStartPos] = useState(null);

  // Desktop states
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const isMobile = useMobile();

  // Conditional state usage
  const menuOpen = isMobile ? mobileMenuOpen : !sidebarCollapsed;
}
```

### Canvas Mobile Optimizations

#### 1. Touch Event Handling

```javascript
// Prevent default touch behaviors
canvas.addEventListener(
  "touchstart",
  (e) => {
    e.preventDefault();
  },
  { passive: false }
);

// Handle touch gestures
canvas.addEventListener("touchmove", (e) => {
  if (e.touches.length === 2) {
    // Handle pinch-to-zoom
    handlePinchZoom(e);
  }
});
```

#### 2. Mobile Canvas Settings

```javascript
const mobileCanvasConfig = {
  // Larger selection tolerance for touch
  targetFindTolerance: 15,

  // Touch-friendly selection borders
  selectionLineWidth: 3,
  selectionDashArray: [8, 8],

  // Disable problematic features
  centeredScaling: false,
  centeredRotation: false,
};
```

## 🎨 Component Development

### Creating Mobile-Responsive Components

#### 1. Component Structure

```javascript
// components/ResponsiveComponent.jsx
import { useMobile } from "@/hooks/useMobile";

function ResponsiveComponent({ children, ...props }) {
  const isMobile = useMobile();

  return (
    <div
      className={`
        base-styles
        ${isMobile ? "mobile-styles" : "desktop-styles"}
      `}
      {...props}
    >
      {children}
    </div>
  );
}

export default ResponsiveComponent;
```

#### 2. Mobile Navigation Pattern

```javascript
// Mobile navigation dengan overlay
function MobileNavigation({ isOpen, onClose, children }) {
  const isMobile = useMobile();

  if (!isMobile || !isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />

      {/* Sidebar */}
      <div className="fixed left-0 top-0 h-full w-80 bg-white z-50 transform transition-transform">
        {children}
      </div>
    </>
  );
}
```

#### 3. Bottom Sheet Pattern

```javascript
// Mobile bottom sheet untuk panels
function BottomSheet({ isOpen, onClose, children }) {
  return (
    <div
      className={`
        fixed bottom-0 left-0 right-0 bg-white border-t z-40
        transform transition-transform duration-300
        ${isOpen ? "translate-y-0" : "translate-y-full"}
      `}
    >
      <div className="max-h-[50vh] overflow-y-auto">{children}</div>
    </div>
  );
}
```

### CSS Utilities untuk Mobile

#### 1. Mobile-Specific Classes

```css
/* globals.css */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-padding {
    padding: 0.5rem !important;
  }
}
```

#### 2. Touch-Friendly Utilities

```css
.touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 12px;
}

.touch-area {
  padding: 16px;
  margin: 8px;
}
```

## 🧪 Testing Guidelines

### Mobile Testing Strategy

#### 1. Device Testing

```javascript
// Test matrix
const testDevices = [
  { name: "iPhone SE", width: 375, height: 667 },
  { name: "iPhone 12", width: 390, height: 844 },
  { name: "iPad", width: 768, height: 1024 },
  { name: "Android Phone", width: 360, height: 640 },
];

// Browser testing
const testBrowsers = [
  "Safari (iOS)",
  "Chrome (Android)",
  "Samsung Browser",
  "Firefox Mobile",
];
```

#### 2. Feature Testing Checklist

```markdown
### Mobile Testing Checklist

#### Navigation

- [ ] Hamburger menu opens/closes
- [ ] Bottom navigation accessible
- [ ] Touch targets ≥44px
- [ ] Smooth animations

#### Canvas

- [ ] Touch drag works
- [ ] Pinch-to-zoom functional
- [ ] Object selection accurate
- [ ] No scroll conflicts

#### Panels

- [ ] Sidebar overlay works
- [ ] Layer panel bottom sheet
- [ ] Settings panels accessible
- [ ] Close gestures work

#### Performance

- [ ] Smooth scrolling
- [ ] No layout shifts
- [ ] Fast touch response
- [ ] Memory usage reasonable
```

### Automated Testing

#### 1. Responsive Testing

```javascript
// cypress/integration/mobile.spec.js
describe("Mobile Responsiveness", () => {
  beforeEach(() => {
    cy.viewport(375, 667); // iPhone SE
  });

  it("should show mobile navigation", () => {
    cy.visit("/design/123");
    cy.get('[data-testid="mobile-nav"]').should("be.visible");
    cy.get('[data-testid="desktop-nav"]').should("not.be.visible");
  });

  it("should handle touch interactions", () => {
    cy.get('[data-testid="canvas"]')
      .trigger("touchstart", { touches: [{ clientX: 100, clientY: 100 }] })
      .trigger("touchmove", { touches: [{ clientX: 150, clientY: 150 }] })
      .trigger("touchend");
  });
});
```

## 🔧 Performance Optimization

### Mobile Performance Best Practices

#### 1. Lazy Loading

```javascript
// Lazy load mobile components
const MobileNavigation = lazy(() => import("./MobileNavigation"));

function App() {
  const isMobile = useMobile();

  return (
    <>
      {isMobile && (
        <Suspense fallback={<div>Loading...</div>}>
          <MobileNavigation />
        </Suspense>
      )}
    </>
  );
}
```

#### 2. Conditional Rendering

```javascript
// Avoid rendering desktop components on mobile
function Layout() {
  const isMobile = useMobile();

  return (
    <div>
      {/* Always render */}
      <Header />

      {/* Conditional rendering */}
      {isMobile ? <MobileLayout /> : <DesktopLayout />}
    </div>
  );
}
```

#### 3. Touch Event Optimization

```javascript
// Debounce touch events
const debouncedTouchHandler = useMemo(
  () => debounce(handleTouch, 16), // 60fps
  [handleTouch]
);

// Passive event listeners
useEffect(() => {
  element.addEventListener("touchmove", handler, { passive: true });
  return () => element.removeEventListener("touchmove", handler);
}, []);
```

## 📚 Resources

### Documentation

- [Mobile Responsive Guide](./MOBILE_RESPONSIVE.md)
- [Component API Reference](./COMPONENTS.md)
- [Canvas API Guide](./CANVAS.md)

### Tools

- [Chrome DevTools Device Mode](https://developer.chrome.com/docs/devtools/device-mode/)
- [Responsive Design Checker](https://responsivedesignchecker.com/)
- [Touch Event Simulator](https://github.com/hammerjs/hammer.js)

### Best Practices

- [Web.dev Mobile Guidelines](https://web.dev/mobile/)
- [Apple Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)
- [Material Design Touch Targets](https://material.io/design/usability/accessibility.html#layout-and-typography)

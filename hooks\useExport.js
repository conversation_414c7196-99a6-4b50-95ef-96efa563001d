import jsPDF from "jspdf";
import { toast } from "sonner";
import { useState } from "react";

export const useExportHook = (canvasEditor, designName = "CanvaInDesign") => {
  const [isExporting, setIsExporting] = useState(false);

  /**
   * Export canvas sebagai PNG
   */
  const exportAsPNG = async (
    qualityMultiplier = 1,
    preserveTransparency = true
  ) => {
    if (!canvasEditor) {
      toast.error("Canvas tidak tersedia");
      return;
    }

    try {
      setIsExporting(true);
      toast.loading("Mengexport sebagai PNG...", { id: "export-png" });

      // Simpan background asli
      const originalBg = canvasEditor.backgroundColor;

      // Jika tidak preserve transparency dan background transparan, set ke putih
      if (
        !preserveTransparency &&
        (originalBg === "transparent" || !originalBg)
      ) {
        canvasEditor.set({ backgroundColor: "#ffffff" });
        canvasEditor.renderAll();
      }

      // Untuk PNG, quality parameter tidak berpengaruh banyak
      // Yang penting adalah multiplier untuk resolusi
      const dataUrl = canvasEditor.toDataURL({
        format: "png",
        quality: 1, // Selalu gunakan kualitas maksimal untuk PNG
        multiplier: qualityMultiplier, // Ini yang menentukan resolusi
      });

      // Restore background asli
      if (
        !preserveTransparency &&
        (originalBg === "transparent" || !originalBg)
      ) {
        canvasEditor.set({ backgroundColor: originalBg });
        canvasEditor.renderAll();
      }

      const link = document.createElement("a");
      link.href = dataUrl;
      link.download = `${designName}.png`;
      link.click();

      toast.success("PNG berhasil diexport!", { id: "export-png" });
    } catch (error) {
      console.error("Error exporting PNG:", error);
      toast.error("Gagal export PNG", { id: "export-png" });
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Export canvas sebagai SVG
   */
  const exportAsSVG = async () => {
    if (!canvasEditor) {
      toast.error("Canvas tidak tersedia");
      return;
    }

    try {
      setIsExporting(true);
      toast.loading("Mengexport sebagai SVG...", { id: "export-svg" });

      const svgData = canvasEditor.toSVG({
        viewBox: {
          x: 0,
          y: 0,
          width: canvasEditor.width,
          height: canvasEditor.height,
        },
        width: canvasEditor.width,
        height: canvasEditor.height,
      });

      const blob = new Blob([svgData], { type: "image/svg+xml" });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `${designName}.svg`;
      link.click();

      URL.revokeObjectURL(url);
      toast.success("SVG berhasil diexport!", { id: "export-svg" });
    } catch (error) {
      console.error("Error exporting SVG:", error);
      toast.error("Gagal export SVG", { id: "export-svg" });
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Export canvas sebagai PDF
   */
  const exportAsPDF = async (format = "a4", orientation = "portrait") => {
    if (!canvasEditor) {
      toast.error("Canvas tidak tersedia");
      return;
    }

    try {
      setIsExporting(true);
      toast.loading("Mengexport sebagai PDF...", { id: "export-pdf" });

      // Get canvas as high quality image (multiplier 2)
      const dataUrl = canvasEditor.toDataURL({
        format: "png",
        quality: 1,
        multiplier: 2,
      });

      // Create PDF
      const pdf = new jsPDF({
        orientation: orientation,
        unit: "mm",
        format: format,
      });

      // Get PDF dimensions
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      // Calculate image dimensions to fit PDF
      const canvasWidth = canvasEditor.width;
      const canvasHeight = canvasEditor.height;
      const ratio = Math.min(pdfWidth / canvasWidth, pdfHeight / canvasHeight);

      const imgWidth = canvasWidth * ratio;
      const imgHeight = canvasHeight * ratio;

      // Center the image
      const x = (pdfWidth - imgWidth) / 2;
      const y = (pdfHeight - imgHeight) / 2;

      pdf.addImage(dataUrl, "PNG", x, y, imgWidth, imgHeight);
      pdf.save(`${designName}.pdf`);

      toast.success("PDF berhasil diexport!", { id: "export-pdf" });
    } catch (error) {
      console.error("Error exporting PDF:", error);
      toast.error("Gagal export PDF", { id: "export-pdf" });
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Export dengan format yang dipilih
   */
  const exportWithFormat = async (format, options = {}) => {
    switch (format) {
      case "png":
        await exportAsPNG(
          options.quality || 1, // Sekarang ini adalah multiplier
          options.preserveTransparency !== undefined
            ? options.preserveTransparency
            : true
        );
        break;
      case "svg":
        await exportAsSVG();
        break;
      case "pdf":
        await exportAsPDF(
          options.format || "a4",
          options.orientation || "portrait"
        );
        break;
      default:
        toast.error("Format tidak didukung");
    }
  };

  return {
    isExporting,
    exportAsPNG,
    exportAsSVG,
    exportAsPDF,
    exportWithFormat,
  };
};

"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import {
  listAllImageKitFiles,
  searchImageKitFiles,
  testCompleteDeleteFlow,
  debugUrlParsing,
} from "@/utils/imagekitDebug";
import { deleteImageKitFile } from "@/utils/imagekitUtils";

/**
 * Debug panel untuk ImageKit operations
 * Hanya untuk development/testing
 */
export default function ImageKitDebugPanel() {
  const [testUrl, setTestUrl] = useState(
    "https://ik.imagekit.io/inuldev0/************************************8474739568.png?updatedAt=1748474743496"
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [results, setResults] = useState("");
  const [loading, setLoading] = useState(false);

  const handleListFiles = async () => {
    setLoading(true);
    setResults("Fetching files...\n");

    try {
      const files = await listAllImageKitFiles(20);
      setResults(
        `Found ${files.length} files:\n\n` +
          files
            .map(
              (file, i) =>
                `${i + 1}. ${file.name}\n   ID: ${file.fileId}\n   URL: ${file.url}\n`
            )
            .join("\n")
      );
    } catch (error) {
      setResults(`Error: ${error.message}`);
    }

    setLoading(false);
  };

  const handleSearchFiles = async () => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    setResults(`Searching for: ${searchQuery}\n`);

    try {
      const files = await searchImageKitFiles(searchQuery);
      setResults(
        `Search results for "${searchQuery}":\n\n` +
          files
            .map((file, i) => `${i + 1}. ${file.name}\n   ID: ${file.fileId}\n`)
            .join("\n")
      );
    } catch (error) {
      setResults(`Error: ${error.message}`);
    }

    setLoading(false);
  };

  const handleDebugUrl = () => {
    if (!testUrl.trim()) return;

    setResults("Debugging URL parsing...\n");
    const parsed = debugUrlParsing(testUrl);

    setResults(
      `URL Debug Results:\n\n` +
        `Original URL: ${parsed.originalUrl}\n` +
        `URL without query: ${parsed.urlWithoutQuery}\n` +
        `Filename: ${parsed.filename}\n` +
        `Name without ext: ${parsed.nameWithoutExt}\n` +
        `Split parts: ${JSON.stringify(parsed.parts)}\n` +
        `Extracted design ID: ${parsed.designId}\n`
    );
  };

  const handleTestDelete = async () => {
    if (!testUrl.trim()) return;

    setLoading(true);
    setResults("Testing delete flow...\n");

    try {
      const success = await testCompleteDeleteFlow(testUrl);
      setResults(
        (prev) =>
          prev + `\nTest completed. Files found: ${success ? "Yes" : "No"}`
      );
    } catch (error) {
      setResults((prev) => prev + `\nError: ${error.message}`);
    }

    setLoading(false);
  };

  const handleActualDelete = async () => {
    if (!testUrl.trim()) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete files for URL: ${testUrl}?`
    );
    if (!confirmed) return;

    setLoading(true);
    setResults("Attempting actual delete...\n");

    try {
      const success = await deleteImageKitFile(testUrl);
      setResults(
        (prev) => prev + `\nDelete result: ${success ? "Success" : "Failed"}`
      );
    } catch (error) {
      setResults((prev) => prev + `\nError: ${error.message}`);
    }

    setLoading(false);
  };

  // Only show in development
  if (process.env.NODE_ENV === "production") {
    return null;
  }

  return (
    <Card className="w-full max-w-4xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>ImageKit Debug Panel</CardTitle>
        <p className="text-sm text-muted-foreground">
          Development tool untuk debug ImageKit operations
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Test URL Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Test URL:</label>
          <Input
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            placeholder="https://ik.imagekit.io/..."
            className="font-mono text-xs"
          />
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <Button
            onClick={handleListFiles}
            disabled={loading}
            variant="outline"
          >
            List Files
          </Button>

          <Button
            onClick={handleDebugUrl}
            disabled={loading || !testUrl.trim()}
            variant="outline"
          >
            Debug URL
          </Button>

          <Button
            onClick={handleTestDelete}
            disabled={loading || !testUrl.trim()}
            variant="outline"
          >
            Test Delete
          </Button>

          <Button
            onClick={handleActualDelete}
            disabled={loading || !testUrl.trim()}
            variant="destructive"
          >
            Actual Delete
          </Button>
        </div>

        {/* Search Section */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Search Query:</label>
          <div className="flex gap-2">
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder='name="filename.png" or name:pattern*'
              className="font-mono text-xs"
            />
            <Button
              onClick={handleSearchFiles}
              disabled={loading || !searchQuery.trim()}
              variant="outline"
            >
              Search
            </Button>
          </div>
        </div>

        {/* Results */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Results:</label>
          <Textarea
            value={results}
            readOnly
            className="font-mono text-xs min-h-[300px]"
            placeholder="Results will appear here..."
          />
        </div>

        {/* Quick Actions */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>
            <strong>Quick Search Examples:</strong>
          </p>
          <p>• Exact filename: name="filename.png"</p>
          <p>• Pattern search: name:j57838na3xdc3668rmdm3esnp17grrq8*</p>
          <p>• Extension search: name:*.png</p>
        </div>
      </CardContent>
    </Card>
  );
}

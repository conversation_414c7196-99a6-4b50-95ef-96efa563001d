import React, { useState } from "react";
import { Keyboard, X } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

function KeyboardShortcuts() {
  const [open, setOpen] = useState(false);

  const shortcuts = [
    {
      category: "Navigation",
      items: [
        { keys: "Arrow Keys", description: "Move Object (1px)" },
        { keys: "Shift + Arrow Keys", description: "Move Object (10px)" },
      ],
    },
    {
      category: "Object Management",
      items: [
        { keys: "Delete", description: "Delete Selected Object" },
        { keys: "Ctrl + L", description: "Lock/Unlock Object" },
        { keys: "Ctrl + H", description: "Hide/Show Object" },
      ],
    },
    {
      category: "Layer Management",
      items: [
        { keys: "Ctrl + ]", description: "Bring Forward" },
        { keys: "Ctrl + [", description: "Send Backward" },
      ],
    },
    {
      category: "Text Editing",
      items: [
        { keys: "Double Click", description: "Edit Text" },
        { keys: "Escape", description: "Exit Text Editing" },
      ],
    },
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Keyboard className="h-4 w-4" />
          Shortcuts
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription>
            Gunakan shortcut ini untuk mempercepat workflow editing Anda
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {shortcuts.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-3">
              <h3 className="font-semibold text-lg text-purple-600">
                {category.category}
              </h3>
              <div className="space-y-2">
                {category.items.map((shortcut, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <span className="text-sm text-gray-700">
                      {shortcut.description}
                    </span>
                    <div className="flex gap-1">
                      {shortcut.keys.split(" + ").map((key, keyIndex) => (
                        <React.Fragment key={keyIndex}>
                          <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-white border border-gray-300 rounded shadow">
                            {key}
                          </kbd>
                          {keyIndex < shortcut.keys.split(" + ").length - 1 && (
                            <span className="text-gray-400 text-xs">+</span>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Tips:</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Tekan Shift sambil drag untuk mempertahankan proporsi</li>
            <li>• Gunakan mouse wheel untuk zoom in/out canvas</li>
            <li>
              • Klik dan drag pada area kosong untuk membuat selection box
            </li>
            <li>• Lock objects untuk mencegah seleksi tidak sengaja</li>
            <li>• Hide objects untuk fokus pada layer tertentu</li>
            <li>• Gunakan Layer Panel untuk kontrol yang lebih detail</li>
          </ul>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default KeyboardShortcuts;

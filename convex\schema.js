import { v } from "convex/values";
import { defineSchema, defineTable } from "convex/server";

export default defineSchema({
  users: defineTable({
    name: v.string(),
    email: v.string(),
    picture: v.string(),
    subscriptionId: v.optional(v.string()),
    subscriptionType: v.optional(
      v.union(v.literal("free"), v.literal("pro"), v.literal("team"))
    ),
    subscriptionStatus: v.optional(
      v.union(
        v.literal("active"),
        v.literal("inactive"),
        v.literal("cancelled")
      )
    ),
    subscriptionExpiresAt: v.optional(v.number()),
  }),

  designs: defineTable({
    name: v.string(),
    width: v.number(),
    height: v.number(),
    jsonTemplate: v.optional(v.any()),
    imagePreview: v.optional(v.string()),
    uid: v.id("users"),
    createdAt: v.optional(v.number()),
  }),

  templates: defineTable({
    name: v.string(),
    imagePreview: v.string(),
    jsonData: v.any(),
    active: v.boolean(),
    width: v.optional(v.number()),
    height: v.optional(v.number()),
    isPremium: v.optional(v.boolean()),
  }),

  subscriptions: defineTable({
    userId: v.id("users"),
    stripeCustomerId: v.optional(v.string()),
    stripeSubscriptionId: v.optional(v.string()),
    stripePriceId: v.optional(v.string()),
    status: v.union(
      v.literal("active"),
      v.literal("inactive"),
      v.literal("cancelled"),
      v.literal("past_due")
    ),
    currentPeriodStart: v.number(),
    currentPeriodEnd: v.number(),
    cancelAtPeriodEnd: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }),
});

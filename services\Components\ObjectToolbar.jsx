import React from "react";
import { Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useCanvasHook } from "@/hooks/useCanvas";
import { useCanvasOperations } from "@/hooks/useCanvasOperations";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

function ObjectToolbar() {
  const { canvasEditor } = useCanvasHook();
  const { deleteObject } = useCanvasOperations(canvasEditor);

  const activeObject = canvasEditor?.getActiveObject();
  const hasSelection = !!activeObject;

  const toolbarItems = [
    {
      icon: Trash2,
      label: "Delete (Del)",
      onClick: deleteObject,
      disabled: !hasSelection,
      shortcut: "Del",
      variant: "destructive",
    },
  ];

  return (
    <div className="flex items-center gap-1 p-2 bg-white border rounded-lg shadow-sm">
      <TooltipProvider>
        {toolbarItems.map((item, index) => (
          <Tooltip key={index}>
            <TooltipTrigger asChild>
              <Button
                variant={item.variant || "ghost"}
                size="sm"
                onClick={item.onClick}
                disabled={item.disabled}
                className="h-8 w-8 p-0"
              >
                <item.icon className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">
                {item.label}
                {item.shortcut && (
                  <span className="ml-2 text-xs opacity-60">
                    {item.shortcut}
                  </span>
                )}
              </p>
            </TooltipContent>
          </Tooltip>
        ))}
      </TooltipProvider>
    </div>
  );
}

export default ObjectToolbar;

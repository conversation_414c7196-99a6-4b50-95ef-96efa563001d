import ImageKit from "imagekit";
import { FabricImage } from "fabric";
import { useParams } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { ImageUp, Loader, AlertCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useCanvasHook } from "@/hooks/useCanvas";
import { toast } from "sonner";

function CustomImageUpload({ selectedAi }) {
  const [image, setImage] = useState(
    "https://ik.imagekit.io/inuldev0/image.png"
  );
  const { designId } = useParams();
  const { canvasEditor } = useCanvasHook();
  const [loading, setLoading] = useState(false);
  const [aiLoading, setAiLoading] = useState(false);
  const [error, setError] = useState(null);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Memoize ImageKit instance untuk mencegah re-creation
  const imagekit = useMemo(
    () =>
      new ImageKit({
        publicKey: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY,
        privateKey: process.env.NEXT_PUBLIC_IMAGEKIT_PRIVATE_KEY,
        urlEndpoint: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT,
      }),
    []
  );

  const onImageUpload = async (event) => {
    let uploadToastId;

    try {
      setLoading(true);
      setError(null);

      const file = event.target.files[0];
      if (!file) {
        toast.error("Tidak ada file yang dipilih");
        return;
      }

      // Validasi file type
      if (!file.type.startsWith("image/")) {
        toast.error("File harus berupa gambar");
        return;
      }

      // Validasi file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Ukuran file maksimal 5MB");
        return;
      }

      uploadToastId = toast.loading("Mengunggah gambar...", {
        duration: Infinity,
      });

      const imageRef = await imagekit.upload({
        file: file,
        fileName: `${designId}_${Date.now()}.png`,
        isPublished: true,
        timeout: 30000, // 30 detik timeout
      });

      toast.dismiss(uploadToastId);
      setImage(imageRef?.url);
      setImageLoaded(false);
      toast.success("Gambar berhasil diunggah", { duration: 3000 });
    } catch (error) {
      console.error("Upload error:", error);

      // Dismiss loading toast jika ada error
      if (uploadToastId) {
        toast.dismiss(uploadToastId);
      }

      setError("Gagal mengunggah gambar");
      toast.error("Gagal mengunggah gambar", { duration: 5000 });
    } finally {
      setLoading(false);
    }
  };

  const onAddToCanvas = async () => {
    let canvasToastId;

    try {
      setLoading(true);
      setError(null);

      if (!image) {
        toast.error("Tidak ada gambar untuk ditambahkan");
        return;
      }

      canvasToastId = toast.loading("Menambahkan gambar ke canvas...", {
        duration: Infinity,
      });

      const canvasImageRef = await FabricImage.fromURL(image, {
        crossOrigin: "anonymous",
        timeout: 15000, // 15 detik timeout
      });

      canvasImageRef.set({
        id: `uploaded-image-${Date.now()}`,
        name: "Uploaded Image",
      });

      toast.dismiss(canvasToastId);
      canvasEditor.add(canvasImageRef);
      canvasEditor.renderAll();
      setImage(null);
      setImageLoaded(false);
      toast.success("Gambar berhasil ditambahkan ke canvas", {
        duration: 3000,
      });
    } catch (error) {
      console.error("Canvas error:", error);

      // Dismiss loading toast jika ada error
      if (canvasToastId) {
        toast.dismiss(canvasToastId);
      }

      setError("Gagal menambahkan gambar ke canvas");
      toast.error("Gagal menambahkan gambar ke canvas", { duration: 5000 });
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk preload image dengan retry mechanism
  const preloadImage = (url, retries = 3) => {
    return new Promise((resolve, reject) => {
      const attemptLoad = (attempt) => {
        const img = new window.Image();
        img.crossOrigin = "anonymous";

        // Timeout yang lebih panjang untuk transformasi berat
        const timeout = setTimeout(
          () => {
            if (attempt < retries) {
              console.log(`Retry attempt ${attempt + 1} for ${url}`);
              attemptLoad(attempt + 1);
            } else {
              reject(new Error("Image preload timeout after retries"));
            }
          },
          30000 + attempt * 10000
        ); // Timeout bertambah setiap retry

        img.onload = () => {
          clearTimeout(timeout);
          resolve(url);
        };

        img.onerror = () => {
          clearTimeout(timeout);
          if (attempt < retries) {
            console.log(`Retry attempt ${attempt + 1} for ${url}`);
            setTimeout(() => attemptLoad(attempt + 1), 2000); // Delay 2 detik sebelum retry
          } else {
            reject(new Error("Image preload failed after retries"));
          }
        };

        img.src = url;
      };

      attemptLoad(0);
    });
  };

  useEffect(() => {
    if (selectedAi && image) {
      const applyTransform = async () => {
        setAiLoading(true);
        setError(null);
        setImageLoaded(false);

        // Simpan toast ID untuk bisa dismiss nanti
        let loadingToastId;

        try {
          let imageUrl = image;

          // Logika untuk menambahkan transformasi (mempertahankan yang sudah ada)
          if (imageUrl.includes("?tr=")) {
            imageUrl = imageUrl + "," + selectedAi.command;
          } else {
            imageUrl = imageUrl + "?tr=" + selectedAi.command;
          }

          console.log("Applying transform:", imageUrl);

          // Berikan feedback berbeda untuk transformasi yang berat
          const heavyTransforms = [
            "e-upscale",
            "bg-genfill",
            "e-changebg-prompt",
          ];
          const isHeavyTransform = heavyTransforms.some((transform) =>
            selectedAi.command.includes(transform)
          );

          // Buat loading toast dengan ID yang bisa di-dismiss
          if (isHeavyTransform) {
            loadingToastId = toast.loading(
              `Menerapkan ${selectedAi.name}... (Ini mungkin membutuhkan waktu lebih lama)`,
              { duration: Infinity } // Tidak auto dismiss
            );
          } else {
            loadingToastId = toast.loading(`Menerapkan ${selectedAi.name}...`, {
              duration: Infinity, // Tidak auto dismiss
            });
          }

          // Preload image untuk memastikan transformasi berhasil
          await preloadImage(imageUrl, isHeavyTransform ? 5 : 3);

          // Dismiss loading toast sebelum show success
          toast.dismiss(loadingToastId);

          setImage(imageUrl);
          setImageLoaded(true);
          toast.success(`${selectedAi.name} berhasil diterapkan!`, {
            duration: 3000,
          });
        } catch (error) {
          console.error("AI Transform error:", error);

          // Dismiss loading toast sebelum show error
          if (loadingToastId) {
            toast.dismiss(loadingToastId);
          }

          setError(
            `Gagal menerapkan ${selectedAi.name}. Coba lagi dalam beberapa saat.`
          );
          toast.error(`Gagal menerapkan ${selectedAi.name}`, {
            duration: 5000,
          });
        } finally {
          setAiLoading(false);
        }
      };

      applyTransform();
    }
  }, [selectedAi]);

  return (
    <div>
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4 flex items-center gap-2">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-700">{error}</span>
        </div>
      )}

      {/* Image Upload Area */}
      {!image ? (
        <label
          htmlFor="uploadImage"
          className={`bg-secondary p-4 flex flex-col items-center justify-center rounded-xl h-[150px] mb-4 cursor-pointer transition-colors hover:bg-secondary/80 ${
            loading ? "opacity-50 cursor-not-allowed" : ""
          }`}
        >
          {loading ? (
            <>
              <Loader className="animate-spin h-6 w-6 mb-2" />
              <h2 className="text-xs">Mengunggah...</h2>
            </>
          ) : (
            <>
              <ImageUp className="h-6 w-6 mb-2" />
              <h2 className="text-xs">Unggah Gambar</h2>
              <p className="text-xs text-gray-500 mt-1">Max 5MB</p>
            </>
          )}
        </label>
      ) : (
        <div className="relative">
          <label htmlFor="uploadImage" className="cursor-pointer block">
            <div className="relative">
              {(aiLoading || !imageLoaded) && (
                <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                  <Loader className="animate-spin h-6 w-6 text-white" />
                </div>
              )}
              <img
                src={image}
                alt="Image"
                className="w-full h-[150px] rounded-lg object-contain border"
                crossOrigin="anonymous"
                onLoad={() => {
                  setImageLoaded(true);
                  console.log("Image Loaded");
                }}
                onError={() => {
                  setError("Gagal memuat gambar");
                  setImageLoaded(false);
                }}
              />
            </div>
          </label>
          {aiLoading && (
            <div className="text-xs text-center mt-2 text-blue-600">
              Menerapkan AI transform...
            </div>
          )}
        </div>
      )}

      <input
        type="file"
        id="uploadImage"
        className="hidden"
        onChange={onImageUpload}
        accept="image/*"
        disabled={loading}
      />

      {/* Action Buttons */}
      {image && (
        <div className="space-y-2 mt-4">
          <Button
            className="w-full"
            size="sm"
            onClick={onAddToCanvas}
            disabled={loading || aiLoading || !imageLoaded}
          >
            {loading ? (
              <>
                <Loader className="animate-spin h-4 w-4 mr-2" />
                Memproses...
              </>
            ) : (
              "Tambahkan ke Canvas"
            )}
          </Button>

          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Reset ke gambar original tanpa transformasi
                let originalUrl = image;
                if (originalUrl.includes("?tr=")) {
                  originalUrl = originalUrl.split("?tr=")[0];
                }
                setImage(originalUrl);
                setImageLoaded(false);
                toast.success("Transformasi dihapus", { duration: 3000 });
              }}
              disabled={loading || aiLoading || !image.includes("?tr=")}
            >
              Reset Transform
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setImage(null);
                setError(null);
                setImageLoaded(false);
              }}
              disabled={loading || aiLoading}
            >
              Reset Gambar
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default CustomImageUpload;

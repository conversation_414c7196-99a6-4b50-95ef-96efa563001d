import React from "react";
import { Menu, Layers } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

function FloatingToggle({
  showSidebarToggle = false,
  showLayerToggle = false,
  onSidebarToggle,
  onLayerToggle,
  objectCount = 0,
}) {
  if (!showSidebarToggle && !showLayerToggle) {
    return null;
  }

  return (
    <div className="fixed right-4 bottom-4 z-50 space-y-2">
      <TooltipProvider>
        {/* Sidebar Toggle */}
        {showSidebarToggle && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={onSidebarToggle}
                className="bg-white shadow-lg hover:shadow-xl transition-all"
              >
                <Menu className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Show Sidebar</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Layer Toggle */}
        {showLayerToggle && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log("FloatingToggle layer button clicked");
                  onLayerToggle();
                }}
                className="bg-white shadow-lg hover:shadow-xl transition-all"
              >
                <Layers className="h-4 w-4 mr-1" />
                {objectCount > 0 && (
                  <span className="text-xs">{objectCount}</span>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Show Layers ({objectCount})</p>
            </TooltipContent>
          </Tooltip>
        )}
      </TooltipProvider>
    </div>
  );
}

export default FloatingToggle;

import { Canvas } from "fabric";
import React, { useEffect, useRef, useState } from "react";

import { useMobile } from "@/hooks/useMobile";
import { useCanvasHook } from "@/hooks/useCanvas";
import { useCanvasOperations } from "@/hooks/useCanvasOperations";

import TopNavBar from "@/services/Components/TopNavBar";
import LayerPanel from "@/services/Components/LayerPanel";
import FloatingToggle from "@/services/Components/FloatingToggle";
import MobileNavigation from "@/services/Components/MobileNavigation";

import SideBar from "./SideBar";

function CanvasEditor({ DesignInfo }) {
  const canvasRef = useRef();
  const { canvasEditor, setCanvasEditor } = useCanvasHook();
  const { moveObjectWithKeys, deleteObject, duplicateObject } =
    useCanvasOperations(canvasEditor);
  const isMobile = useMobile();

  // State untuk toggle panels
  const [showFloatingSidebar, setShowFloatingSidebar] = useState(false);
  const [showFloatingLayer, setShowFloatingLayer] = useState(false);
  const [objectCount, setObjectCount] = useState(0);
  const [layerPanelMinimized, setLayerPanelMinimized] = useState(false);

  // Mobile specific states
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const [mobileLayerOpen, setMobileLayerOpen] = useState(false);

  /**
   * Gunakan untuk menginisialisasi canvas
   */
  useEffect(() => {
    if (canvasRef.current && DesignInfo) {
      const initCanvas = new Canvas(canvasRef.current, {
        width: DesignInfo?.width,
        height: DesignInfo?.height,
        backgroundColor: "#fff",
        preserveObjectStacking: true,
        controlsAboveOverlay: true,
        // Enable multi-selection
        selection: true,
        selectionBorderColor: "#4f46e5",
        selectionLineWidth: 2,
        selectionDashArray: [5, 5],
        // Enable object caching for better performance
        enableRetinaScaling: true,
        // Allow objects to be selected with click
        interactive: true,
        // Mobile optimizations
        ...(isMobile && {
          // Touch-friendly selection
          selectionLineWidth: 3,
          selectionDashArray: [8, 8],
          // Better touch handling
          targetFindTolerance: 15,
          // Disable some features that don't work well on mobile
          centeredScaling: false,
          centeredRotation: false,
        }),
      });

      if (DesignInfo?.jsonTemplate) {
        initCanvas.loadFromJSON(DesignInfo?.jsonTemplate, () => {
          initCanvas?.requestRenderAll();
        });
      }

      initCanvas.renderAll();

      // Mobile touch optimizations
      if (isMobile) {
        // Prevent default touch behaviors
        const canvasElement = initCanvas.getElement();
        canvasElement.addEventListener(
          "touchstart",
          (e) => {
            e.preventDefault();
          },
          { passive: false }
        );

        canvasElement.addEventListener(
          "touchmove",
          (e) => {
            e.preventDefault();
          },
          { passive: false }
        );

        // Add pinch-to-zoom support (basic implementation)
        let lastTouchDistance = 0;
        canvasElement.addEventListener("touchstart", (e) => {
          if (e.touches.length === 2) {
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            lastTouchDistance = Math.sqrt(
              Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
          }
        });

        canvasElement.addEventListener("touchmove", (e) => {
          if (e.touches.length === 2) {
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const currentDistance = Math.sqrt(
              Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );

            if (lastTouchDistance > 0) {
              const scale = currentDistance / lastTouchDistance;
              const zoom = initCanvas.getZoom() * scale;

              // Limit zoom levels
              if (zoom >= 0.5 && zoom <= 3) {
                initCanvas.setZoom(zoom);
                initCanvas.requestRenderAll();
              }
            }
            lastTouchDistance = currentDistance;
          }
        });
      }

      setCanvasEditor(initCanvas);

      return () => {
        initCanvas.dispose();
      };
    }
  }, [DesignInfo, isMobile]);

  /**
   * Gunakan untuk keyboard shortcuts dan object operations
   */
  useEffect(() => {
    const handleKeyDown = (event) => {
      console.log(
        "Key pressed:",
        event.key,
        "Ctrl:",
        event.ctrlKey,
        "Shift:",
        event.shiftKey
      );

      if (!canvasEditor) {
        console.log("No canvas editor available");
        return;
      }

      const activeObject = canvasEditor.getActiveObject();
      const isEditing = activeObject?.isEditing;

      console.log("Active object:", activeObject, "Is editing:", isEditing);

      // Jangan proses shortcuts jika sedang editing text
      if (isEditing) {
        console.log("Text editing mode, skipping shortcuts");
        return;
      }

      // Delete object
      if (event.key === "Delete" && activeObject) {
        console.log("Deleting object");
        deleteObject();
        return;
      }

      // Move objects with arrow keys
      if (
        ["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(
          event.key
        ) &&
        activeObject
      ) {
        console.log("Moving object with arrow key");
        event.preventDefault();
        moveObjectWithKeys(event.key, event.shiftKey ? 10 : 1);
        return;
      }

      // Layer management shortcuts
      if (activeObject && event.ctrlKey) {
        switch (event.key) {
          case "d":
          case "D":
            // Ctrl+D: Duplicate object
            event.preventDefault();
            console.log("Duplicating object");
            duplicateObject();
            return;

          case "l":
          case "L":
            // Ctrl+L: Toggle lock
            event.preventDefault();
            const isLocked = activeObject.selectable === false;
            activeObject.set({
              selectable: isLocked,
              evented: isLocked,
            });
            if (!isLocked) {
              canvasEditor.discardActiveObject();
            }
            canvasEditor.requestRenderAll();
            console.log(`Object ${isLocked ? "unlocked" : "locked"}`);
            return;

          case "h":
          case "H":
            // Ctrl+H: Toggle visibility
            event.preventDefault();
            const isVisible = activeObject.visible !== false;
            activeObject.set({ visible: !isVisible });
            if (!isVisible) {
              canvasEditor.discardActiveObject();
            }
            canvasEditor.requestRenderAll();
            console.log(`Object ${isVisible ? "hidden" : "shown"}`);
            return;

          case "]":
            // Ctrl+]: Bring forward
            event.preventDefault();
            canvasEditor.bringForward(activeObject);
            canvasEditor.requestRenderAll();
            console.log("Object brought forward");
            return;

          case "[":
            // Ctrl+[: Send backward
            event.preventDefault();
            canvasEditor.sendBackwards(activeObject);
            canvasEditor.requestRenderAll();
            console.log("Object sent backward");
            return;
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [canvasEditor, moveObjectWithKeys, deleteObject, duplicateObject]);

  /**
   * Track object count untuk floating toggle
   */
  useEffect(() => {
    if (!canvasEditor) return;

    const updateObjectCount = () => {
      const objects = canvasEditor.getObjects();
      setObjectCount(objects.length);
    };

    // Initial count
    updateObjectCount();

    // Listen to canvas events
    canvasEditor.on("object:added", updateObjectCount);
    canvasEditor.on("object:removed", updateObjectCount);

    return () => {
      canvasEditor.off("object:added", updateObjectCount);
      canvasEditor.off("object:removed", updateObjectCount);
    };
  }, [canvasEditor]);

  // Handle layer panel minimize state
  useEffect(() => {
    console.log(
      "CanvasEditor layerPanelMinimized changed to:",
      layerPanelMinimized
    );
    setShowFloatingLayer(layerPanelMinimized);
  }, [layerPanelMinimized]);

  return (
    <div className="bg-secondary w-full min-h-screen flex flex-col relative">
      {/* Mobile Navigation */}
      <MobileNavigation
        onSidebarToggle={() => setMobileSidebarOpen(true)}
        onLayerToggle={() => setMobileLayerOpen(!mobileLayerOpen)}
        objectCount={objectCount}
        showLayerButton={true}
      />

      {/* Desktop TopNavBar - Hidden on mobile */}
      <div className={isMobile ? "mobile-hidden" : ""}>
        <TopNavBar />
      </div>

      <div className={`flex flex-1 ${isMobile ? "pt-16 pb-16" : "mt-10"}`}>
        {/* Sidebar - Conditional rendering untuk mobile */}
        <SideBar
          {...(isMobile && {
            isOpen: mobileSidebarOpen,
            onClose: () => setMobileSidebarOpen(false),
          })}
        />

        {/* Canvas Area */}
        <div
          className={`flex-1 flex items-start justify-center relative overflow-auto ${
            isMobile ? "mobile-canvas-container" : "p-4"
          }`}
        >
          <div className="flex items-center justify-center min-h-full">
            <canvas
              id="canvas"
              ref={canvasRef}
              className={isMobile ? "max-w-full" : ""}
              style={isMobile ? { touchAction: "none" } : {}}
            />
          </div>
        </div>

        {/* Desktop Layer Panel - Hidden on mobile */}
        {!isMobile && (
          <LayerPanel
            isMinimized={layerPanelMinimized}
            onMinimize={setLayerPanelMinimized}
          />
        )}
      </div>

      {/* Mobile Layer Panel */}
      {isMobile && (
        <div className={`mobile-layer-panel ${mobileLayerOpen ? "open" : ""}`}>
          <LayerPanel
            isMinimized={false}
            onMinimize={() => setMobileLayerOpen(false)}
            isMobile={true}
          />
        </div>
      )}

      {/* Desktop Floating Toggle Buttons - Hidden on mobile */}
      {!isMobile && (
        <FloatingToggle
          showSidebarToggle={showFloatingSidebar}
          showLayerToggle={showFloatingLayer}
          onSidebarToggle={() => setShowFloatingSidebar(false)}
          onLayerToggle={() => setLayerPanelMinimized(false)}
          objectCount={objectCount}
        />
      )}
    </div>
  );
}

export default CanvasEditor;

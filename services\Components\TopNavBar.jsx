import { useEffect, useState, useCallback } from "react";

import { useCanvasHook } from "@/hooks/useCanvas";

import TextSettingsNavbar from "./TextSettingsNavbar";
import ShapesSettings from "../Sharable/ShapesSettings";
import KeyboardShortcuts from "./KeyboardShortcuts";

function TopNavBar() {
  const { canvasEditor } = useCanvasHook();
  const [showShapeSettings, setShowShapeSettings] = useState(false);
  const [enableTextSettings, setEnableTextSettings] = useState(false);

  const updateShapeSettings = useCallback((activeObject) => {
    setShowShapeSettings(
      activeObject &&
        activeObject.type !== "i-text" &&
        activeObject.type !== "textbox"
    );
  }, []);

  const updateTextSettings = useCallback((activeObject) => {
    setEnableTextSettings(
      activeObject &&
        (activeObject.type === "i-text" || activeObject.type === "textbox")
    );
  }, []);

  const handleSelectionCreated = useCallback(() => {
    const activeObject = canvasEditor?.getActiveObject();
    updateShapeSettings(activeObject);
    updateTextSettings(activeObject);
  }, [canvasEditor, updateShapeSettings, updateTextSettings]);

  const handleSelectionUpdated = useCallback(() => {
    const activeObject = canvasEditor?.getActiveObject();
    updateShapeSettings(activeObject);
    updateTextSettings(activeObject);
  }, [canvasEditor, updateShapeSettings, updateTextSettings]);

  const handleSelectionCleared = useCallback(() => {
    setShowShapeSettings(false);
    setEnableTextSettings(false);
  }, []);

  useEffect(() => {
    if (!canvasEditor) return;

    // Initial check
    const activeObject = canvasEditor.getActiveObject();
    updateShapeSettings(activeObject);
    updateTextSettings(activeObject);

    // Add event listeners
    canvasEditor.on("selection:created", handleSelectionCreated);
    canvasEditor.on("selection:updated", handleSelectionUpdated);
    canvasEditor.on("selection:cleared", handleSelectionCleared);

    // Cleanup function
    return () => {
      canvasEditor.off("selection:created", handleSelectionCreated);
      canvasEditor.off("selection:updated", handleSelectionUpdated);
      canvasEditor.off("selection:cleared", handleSelectionCleared);
    };
  }, [
    canvasEditor,
    handleSelectionCreated,
    handleSelectionUpdated,
    handleSelectionCleared,
    updateShapeSettings,
    updateTextSettings,
  ]);

  return (
    <div className="sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {showShapeSettings ? (
              <div className="flex items-center justify-start">
                <ShapesSettings />
              </div>
            ) : enableTextSettings ? (
              <div className="flex items-center justify-start">
                <TextSettingsNavbar />
              </div>
            ) : (
              <div className="flex items-center justify-start text-gray-500 text-sm">
                Pilih objek untuk menampilkan pengaturan
              </div>
            )}
          </div>

          {/* Keyboard Shortcuts Help */}
          <div className="flex items-center">
            <KeyboardShortcuts />
          </div>
        </div>
      </div>
    </div>
  );
}

export default TopNavBar;

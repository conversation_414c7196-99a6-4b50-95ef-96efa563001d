import { useCanvasHook } from "@/hooks/useCanvas";

function MoveBackward() {
  const { canvasEditor } = useCanvasHook();

  const MoveBackward = () => {
    const activeObject = canvasEditor.getActiveObject();
    if (activeObject) {
      canvasEditor.sendObjectBackwards(activeObject);
    }
  };

  return (
    <div onClick={MoveBackward} className="cursor-pointer">
      Tampak Belakang
    </div>
  );
}

export default MoveBackward;

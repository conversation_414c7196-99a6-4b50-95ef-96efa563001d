import { v } from "convex/values";

import { mutation, query } from "./_generated/server";

export const CreateNewUser = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    picture: v.string(),
  },
  handler: async (ctx, args) => {
    // cek jika user sudah ada
    const userData = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), args.email))
      .collect();
    // jika user belum ada, maka buat user baru
    if (userData?.length == 0) {
      const result = await ctx.db.insert("users", {
        name: args.name,
        email: args.email,
        picture: args.picture,
        subscriptionType: "free",
        subscriptionStatus: "active",
      });

      return result;
    }

    return userData[0];
  },
});

export const GetUserSubscriptionInfo = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User tidak ditemukan");
    }

    return {
      subscriptionType: user.subscriptionType || "free",
      subscriptionStatus: user.subscriptionStatus || "active",
      subscriptionExpiresAt: user.subscriptionExpiresAt,
      isPremium:
        user.subscriptionType === "pro" || user.subscriptionType === "team",
    };
  },
});

export const UpdateUserSubscription = mutation({
  args: {
    userId: v.id("users"),
    subscriptionType: v.union(
      v.literal("free"),
      v.literal("pro"),
      v.literal("team")
    ),
    subscriptionStatus: v.union(
      v.literal("active"),
      v.literal("inactive"),
      v.literal("cancelled")
    ),
    subscriptionExpiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const result = await ctx.db.patch(args.userId, {
      subscriptionType: args.subscriptionType,
      subscriptionStatus: args.subscriptionStatus,
      subscriptionExpiresAt: args.subscriptionExpiresAt,
    });

    return result;
  },
});

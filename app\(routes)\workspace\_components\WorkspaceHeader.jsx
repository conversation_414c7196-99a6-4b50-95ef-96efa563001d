"use client";

import React, { useContext } from "react";
import Image from "next/image";
import { useQuery } from "convex/react";
import { UserButton } from "@stackframe/stack";

import { UserDetailContext } from "@/context/UserDetailContext";
import { api } from "@/convex/_generated/api";
import SubscriptionBadge from "@/components/ui/subscription-badge";

function WorkspaceHeader() {
  const { userDetail } = useContext(UserDetailContext);

  // Get user subscription info
  const subscriptionInfo = useQuery(
    api.users.GetUserSubscriptionInfo,
    userDetail?._id ? { userId: userDetail._id } : "skip"
  );

  return (
    <div className="p-2 flex justify-between items-center bg-gradient-to-r from-purple-700 via-blue-400 to-sky-600">
      <div className="flex items-center gap-3">
        <Image src={"/logo.png"} alt="logo" width={100} height={60} />
        {subscriptionInfo && (
          <SubscriptionBadge
            subscriptionType={subscriptionInfo.subscriptionType}
            className="text-white border-white/20"
          />
        )}
      </div>
      <UserButton />
    </div>
  );
}

export default WorkspaceHeader;

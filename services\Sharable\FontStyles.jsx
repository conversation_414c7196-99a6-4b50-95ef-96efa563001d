import { Bold, Italic, Underline } from "lucide-react";

import { Toggle } from "@/components/ui/toggle";
import { useCanvasHook } from "@/hooks/useCanvas";

function FontStyles() {
  const { canvasEditor } = useCanvasHook();
  const activeObject = canvasEditor?.getActiveObject();

  const onSettingClick = (type) => {
    const activeObject = canvasEditor?.getActiveObject();
    if (
      activeObject &&
      (activeObject.type === "i-text" || activeObject.type === "textbox")
    ) {
      if (type == "bold") {
        activeObject.set({
          fontWeight: activeObject?.fontWeight == "bold" ? "normal" : "bold",
        });
      }
      if (type == "italic") {
        activeObject.set({
          fontStyle: activeObject?.fontStyle == "italic" ? "normal" : "italic",
        });
      }
      if (type == "underline") {
        activeObject.set({
          underline: activeObject?.underline ? false : true,
        });
      }

      canvasEditor.renderAll();
    }
  };

  return (
    <div>
      <Toggle
        aria-label="Toggle bold"
        defaultPressed={activeObject?.fontWeight == "bold"}
        onClick={() => onSettingClick("bold")}
      >
        <Bold className="h-4 w-4" />
      </Toggle>
      <Toggle
        aria-label="Toggle italic"
        defaultPressed={activeObject?.fontStyle == "italic"}
        onClick={() => onSettingClick("italic")}
      >
        <Italic className="h-4 w-4" />
      </Toggle>
      <Toggle
        aria-label="Toggle underline"
        defaultPressed={activeObject?.underline}
        onClick={() => onSettingClick("underline")}
      >
        <Underline className="h-4 w-4" />
      </Toggle>
    </div>
  );
}

export default FontStyles;

"use client";

import Link from "next/link";
import { useContext } from "react";
import { useQuery } from "convex/react";
import { Crown, Zap, AlertCircle } from "lucide-react";

import { api } from "@/convex/_generated/api";
import { UserDetailContext } from "@/context/UserDetailContext";
import {
  getSubscriptionDisplayInfo,
  getRemainingDesigns,
  FREE_LIMITS,
} from "@/lib/subscription";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

function SubscriptionStatus() {
  const { userDetail } = useContext(UserDetailContext);

  // Get user subscription info
  const subscriptionInfo = useQuery(
    api.users.GetUserSubscriptionInfo,
    userDetail?._id ? { userId: userDetail._id } : "skip"
  );

  // Get user design count
  const designCount = useQuery(
    api.designs.GetUserDesignCount,
    userDetail?._id ? { uid: userDetail._id } : "skip"
  );

  if (!subscriptionInfo || designCount === undefined || designCount === null) {
    return (
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="animate-pulse flex space-x-4">
            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
            <div className="flex-1 space-y-2 py-1">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const subscriptionType = subscriptionInfo.subscriptionType;
  const isPremium = subscriptionInfo.isPremium;
  const displayInfo = getSubscriptionDisplayInfo(subscriptionType);
  const remainingDesigns = getRemainingDesigns(
    subscriptionType,
    designCount || 0
  );

  return (
    <Card className="mb-6 border-l-4 border-l-purple-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div
              className={`p-2 rounded-lg ${isPremium ? "bg-purple-100" : "bg-gray-100"}`}
            >
              {isPremium ? (
                <Crown className="h-5 w-5 text-purple-600" />
              ) : (
                <Zap className="h-5 w-5 text-gray-600" />
              )}
            </div>
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                Status Akun
                <Badge
                  variant={isPremium ? "default" : "secondary"}
                  className={isPremium ? "bg-purple-600" : ""}
                >
                  {displayInfo.badge}
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">
                {isPremium
                  ? "Akses penuh ke semua fitur"
                  : "Akun gratis dengan batasan"}
              </p>
            </div>
          </div>

          {!isPremium && (
            <Link href="/workspace/billing">
              <Button className="bg-purple-600 hover:bg-purple-700">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade
              </Button>
            </Link>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Design Limit Progress */}
        {!isPremium && (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Desain yang Dibuat</span>
              <span className="text-gray-600">
                {designCount} / {FREE_LIMITS.MAX_DESIGNS}
              </span>
            </div>

            <Progress
              value={(designCount / FREE_LIMITS.MAX_DESIGNS) * 100}
              className="h-2"
            />

            {remainingDesigns <= 1 && (
              <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <div className="text-sm">
                  <span className="font-medium text-amber-800">
                    {remainingDesigns === 0
                      ? "Limit desain tercapai!"
                      : `Tersisa ${remainingDesigns} desain lagi`}
                  </span>
                  <p className="text-amber-700">
                    Upgrade ke Pro untuk membuat desain unlimited.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Features List */}
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Fitur Tersedia:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {displayInfo.features.map((feature, index) => (
              <div
                key={index}
                className="flex items-center gap-2 text-sm text-gray-600"
              >
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                {feature}
              </div>
            ))}
          </div>
        </div>

        {/* Premium Benefits Preview */}
        {!isPremium && (
          <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="text-sm font-medium text-purple-800 mb-2">
              Upgrade ke Pro dan dapatkan:
            </h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• Unlimited desain</li>
              <li>• Export SVG & PDF</li>
              <li>• Template premium</li>
              <li>• Prioritas support</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default SubscriptionStatus;
